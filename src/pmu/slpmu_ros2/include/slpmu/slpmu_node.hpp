/**
 * slpmu_node.hpp
 * Slamtec PMU ROS2 node
 *
 * Created by <PERSON> <<EMAIL>> on 2025-08-24
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#pragma once

#include "operators/operators.hpp"
#include <slpmu/slpmu_process.hpp>
#include <slpmu/jack/i_jack_controller.hpp>

#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <sensor_msgs/msg/imu.hpp>
#include <sensor_msgs/msg/joint_state.hpp>
#include "slpmu_ros2/action/jack_control.hpp"
#include "slpmu_ros2/msg/jack_status.hpp"
#include "slpmu_ros2/msg/motor_state.hpp"
#include <tf2_ros/transform_broadcaster.h>

namespace slpmu::node {

class PMUNode : public rclcpp::Node {
public:
    using JackControlAction = slpmu_ros2::action::JackControl;
    using GoalHandleJackControl = rclcpp_action::ServerGoalHandle<JackControlAction>;

    PMUNode(const rclcpp::NodeOptions& options = rclcpp::NodeOptions());
    virtual ~PMUNode() = default;

private:
    void init_parameters();
    bool init_devices();
    void init_process();
    void init_operators();
    void init_topics();
    void start();

private:
    void on_motor_position(const operators::ControlOperator::motor_positions_t& motor_pos);
    void on_motor_speed(const operators::ControlOperator::motor_speeds_t& motor_speed);
    void on_gpio_inputs(const operators::ControlOperator::gpio_inputs_t& gpio_inputs);

    // Jack operator callbacks
    void on_jack_position(const operators::JackOperator::jack_position_t& position);
    void on_jack_status(const operators::JackOperator::jack_status_t& status);
    void on_jack_alarm(const operators::JackOperator::jack_alarm_t& alarm);

    // Jack action server callbacks
    rclcpp_action::GoalResponse handle_jack_goal(
        const rclcpp_action::GoalUUID & uuid,
        std::shared_ptr<const JackControlAction::Goal> goal);

    rclcpp_action::CancelResponse handle_jack_cancel(
        const std::shared_ptr<GoalHandleJackControl> goal_handle);

    void handle_jack_accepted(const std::shared_ptr<GoalHandleJackControl> goal_handle);

    void execute_jack_goal(const std::shared_ptr<GoalHandleJackControl> goal_handle);
    void on_motor_alarm_status(const operators::ControlOperator::motor_alarm_status_t& alarm_status);

    void on_tick();
private:
    // parameters
    double control_freq_ { 40. };

    std::string can_interface_ { "can0" };
    size_t motor_ctrl_node_id_ { 1 };
    double wheel_diameter_left_ { 0.14 };
    double wheel_diameter_right_ { 0.14 };
    double wheel_span_ { 0.39 };
    double gear_ratio_ { 1.0 };
    int encoder_resolution_ { 16384 };

    std::string odom_frame_id_ { "odom" };
    std::string base_frame_id_ { "base_link" };

    std::string cmd_vel_topic_ { "cmd_vel" };
    std::string odom_topic_ { "odom" };
    std::string filtered_imu_topic_ { "imu/filtered" };
    std::string joint_state_topic_ { "joint_state" };

    // timeout parameters
    int cmd_vel_timeout_ms_ { 500 };
    std::string jack_status_topic_ { "jack_status" };

    // Jack parameters
    bool jack_enable_ { false };
    std::string jack_can_interface_ { "can0" };
    std::uint16_t jack_node_id_ { 7 };

    // devices
    std::shared_ptr<IMotorController> motor_ctrl_;
    std::shared_ptr<IJackController> jack_ctrl_;

    // process
    std::unique_ptr<slpmu::process::ImuOdomFusion> imu_odom_fusion_;

    // operators
    std::unique_ptr<operators::ControlOperator> control_operator_;
    std::shared_ptr<operators::JackOperator> jack_operator_;
    std::vector<std::shared_ptr<operators::IOperator>> operators_;
    std::unique_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_;

    // topics
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_sub_;
    rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr odom_pub_;
    rclcpp::Publisher<sensor_msgs::msg::JointState>::SharedPtr joint_state_pub_;
    rclcpp::Subscription<sensor_msgs::msg::Imu>::SharedPtr filtered_imu_sub_;
    rclcpp::Publisher<slpmu_ros2::msg::JackStatus>::SharedPtr jack_status_pub_;

    // Jack action server
    rclcpp_action::Server<JackControlAction>::SharedPtr jack_action_server_;
    std::shared_ptr<GoalHandleJackControl> current_jack_goal_handle_;
    std::mutex jack_action_mutex_;

    rclcpp::Publisher<slpmu_ros2::msg::MotorState>::SharedPtr motor_state_pub_;

    // timers
    rclcpp::TimerBase::SharedPtr tick_timer_;
    
    // Joint state tracking
    std::int32_t last_left_position_ {0};
    std::int32_t last_right_position_ {0};
    double last_left_speed_rad_s_ {0.0};
    double last_right_speed_rad_s_ {0.0};
    std::int64_t last_position_timestamp_ns_ {0};

    // Timeout and state tracking
    rclcpp::Time last_cmd_vel_time_;
    geometry_msgs::msg::Twist latest_cmd_vel_;

    // Jack state tracking
    std::int32_t last_jack_position_ {0};
    std::uint16_t last_jack_status_ {0};
    std::uint16_t last_jack_alarm_ {0};
    std::string current_jack_stage_ {"init"};
    std::mutex jack_state_mutex_;
    std::int32_t jack_max_distance_ {26000000};
    std::uint32_t jack_default_speed_ {1000};
    double jack_control_period_ms_ {100.};
    std::uint32_t last_motor_alarm_status_ {0};

};

}
