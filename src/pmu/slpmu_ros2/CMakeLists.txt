cmake_minimum_required(VERSION 3.8)
project(slpmu_ros2)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(rclcpp_action REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(slcan REQUIRED)
find_package(slpmu_motor REQUIRED)
find_package(slpmu_process REQUIRED)
find_package(slpmu_jack REQUIRED)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

# Generate action and message interfaces
rosidl_generate_interfaces(${PROJECT_NAME}
  "action/JackControl.action"
  "msg/JackStatus.msg"
  "msg/MotorState.msg"
  DEPENDENCIES std_msgs
)
rosidl_get_typesupport_target(cpp_typesupport_target "${PROJECT_NAME}" "rosidl_typesupport_cpp")

file(GLOB_RECURSE OPERATORS_SRCS src/operators/*.cpp)

# Declare a C++ executable for PMU motor node
add_executable(slpmu_node src/slpmu_node.cpp src/slpmu_ros2_main.cpp ${OPERATORS_SRCS})
target_include_directories(slpmu_node PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>)
target_compile_features(slpmu_node PUBLIC c_std_99 cxx_std_17)
target_link_libraries(slpmu_node
  slcan
  slpmu_motor
  slpmu_process
  slpmu_jack
  "${cpp_typesupport_target}")
ament_target_dependencies(
  slpmu_node
  "rclcpp"
  "rclcpp_action"
  "std_msgs"
  "sensor_msgs"
  "geometry_msgs"
  "nav_msgs"
  "tf2"
  "tf2_ros"
  "tf2_geometry_msgs"
)

# Install executables
install(TARGETS
  slpmu_node
  DESTINATION lib/${PROJECT_NAME})

# Add install for launch files and config files
install(DIRECTORY
  launch
  config
  DESTINATION share/${PROJECT_NAME}
  OPTIONAL
)

# Export dependencies for generated interfaces
find_package(rosidl_default_runtime REQUIRED)
ament_export_dependencies(rosidl_default_runtime)

ament_package()