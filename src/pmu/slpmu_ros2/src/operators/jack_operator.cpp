/**
 * jack_operator.cpp
 * Jack controlling operator implementation
 *
 * Created by Augment Agent on 2025-08-27
 * Copyright 2025 (c) Shanghai Slamtec Co., Ltd.
 */

#include <slpmu/operators/jack_operator.hpp>
#include <iostream>
#include <chrono>
#include <algorithm>

namespace slpmu::operators {

namespace {
static constexpr std::int32_t kPositionTolerance = 1000;  // encoder counts
}

JackOperator::JackOperator(std::shared_ptr<slpmu::IJackController> jack_ctrl, std::int64_t period_ns)
    : jack_ctrl_(std::move(jack_ctrl))
    , period_ns_(period_ns)
{
}

std::optional<TimeWindow> JackOperator::requested_time_window(std::int64_t remaining_time_ns)
{
    std::int64_t now_ns = std::chrono::steady_clock::now().time_since_epoch().count();

    auto next_run_ns = last_run_ns_ + period_ns_;
    if (next_run_ns > now_ns + remaining_time_ns) {
        return std::nullopt;
    }


    TimeWindow window;
    window.starts_in_ns = 0;
    window.duration_in_ns = kRoundTripDurationInNs * 3;

    last_run_ns_ = now_ns;

    return window;
}

std::string JackOperator::stage_to_string(Stage s)
{
    switch (s) {
        case Stage::Init: return "init";
        case Stage::DetectingBase: return "detecting_base";
        case Stage::BaseStop: return "base_stop";
        case Stage::LiftingUp: return "lifting_up";
        case Stage::LiftingDown: return "lifting_down";
        case Stage::TopStop: return "top_stop";
        case Stage::MiddleStop: return "middle_stop";
        default: return "unknown";
    }
}

void JackOperator::tick()
{
    if (!jack_ctrl_ || !jack_ctrl_->is_open()) {
        return;
    }

    if (current_stage_ == Stage::Init) {
        start_action(ActionRequest{"detect_base"}, nullptr, nullptr);
    }
    // Read jack position
    auto position = jack_ctrl_->read_actual_position();
    if (position.success) {
        current_position_ = position.value;
    }
    if (position_callback_) {
        position_callback_(position);
    }

    // Read jack status
    auto status = jack_ctrl_->read_status();
    if (status.success) {
        current_status_ = status.value;
    }
    if (status_callback_) {
        status_callback_(status);
    }

    // Read jack alarm
    auto alarm = jack_ctrl_->read_alarm();
    if (alarm.success) {
        current_alarm_ = alarm.value;
    }
    if (alarm_callback_) {
        alarm_callback_(alarm);
    }

    if (stage_callback_) {
        stage_callback_(stage_to_string(current_stage_));
    }

    // Handle ongoing action
    feedback_callback_t fb_cb;
    result_callback_t res_cb;
    ActionRequest action_req;
    Stage current_stage;
    bool active = false;
    bool started = false;
    bool cancel_req = false;

    {
        std::lock_guard<std::mutex> lk(action_mutex_);
        active = action_active_;
        started = action_started_;
        cancel_req = cancel_requested_;
        fb_cb = feedback_cb_;
        res_cb = result_cb_;
        action_req = action_req_;
        current_stage = current_stage_;
    }

    if (!active) {
        return;
    }

    // Helper to publish feedback
    auto publish_feedback = [&](Stage s) {
        if (s != current_stage) {
            current_stage = s;
        }
        if (fb_cb) {
            Feedback fb;
            fb.current_stage = stage_to_string(current_stage);
            fb.current_position = current_position_;
            fb.current_status = current_status_;
            fb.current_alarm = current_alarm_;
            // Progress estimation
            if (action_req.command == "lift_up" && action_req.target_position > 0) {
                float prog = 0.f;
                if (action_req.target_position > 0) {
                    prog = std::clamp(100.f * (static_cast<float>(current_position_) / static_cast<float>(action_req.target_position)), 0.f, 100.f);
                }
                fb.progress = prog;
            } else if (action_req.command == "lift_down") {
                // Assume target is 0
                // crude: progress proportional to distance to zero
                fb.progress = std::clamp(100.f * (1.f - (static_cast<float>(current_position_) / static_cast<float>(std::max(1, action_req.target_position)))) , 0.f, 100.f);
            } else if (action_req.command == "detect_base") {
                fb.progress = (current_stage == Stage::BaseStop) ? 100.f : 50.f;
            } else if (action_req.command == "stop") {
                fb.progress = 100.f;
            } else if (action_req.command == "clear_alarm") {
                fb.progress = (current_alarm_ == 0) ? 100.f : 0.f;
            }
            fb_cb(fb);
        }
    };

    // Cancellation handling
    if (cancel_req) {
        bool brk = cmd_brake();
        Stage new_stage = Stage::MiddleStop;
        {
            std::lock_guard<std::mutex> lk(action_mutex_);
            current_stage_ = new_stage;
            action_active_ = false;
            action_started_ = false;
            cancel_requested_ = false;
        }
        publish_feedback(new_stage);
        if (res_cb) {
            ActionResult res;
            res.success = brk;
            res.canceled = true;
            res.message = brk ? "Canceled and stopped" : "Cancel requested but brake failed";
            res.final_position = current_position_;
            res.final_status = current_status_;
            res.alarm_code = current_alarm_;
            res_cb(res);
        }
        return;
    }

    // Start action on first tick
    if (!started) {
        bool ok = false;
        if (action_req.command == "detect_base") {
            ok = cmd_detect_base();
            current_stage = Stage::DetectingBase;
        } else if (action_req.command == "lift_up") {
            ok = cmd_lift_up(action_req.target_position, action_req.speed_rpm);
            current_stage = Stage::LiftingUp;
        } else if (action_req.command == "lift_down") {
            ok = cmd_lift_down(action_req.speed_rpm);
            current_stage = Stage::LiftingDown;
        } else if (action_req.command == "stop") {
            ok = cmd_brake();
            current_stage = Stage::MiddleStop;
        } else if (action_req.command == "clear_alarm") {
            ok = cmd_clear_alarm();
            // keep stage unchanged
        }

        {
            std::lock_guard<std::mutex> lk(action_mutex_);
            action_started_ = ok;
            current_stage_ = current_stage;
        }

        publish_feedback(current_stage);

        if (!ok) {
            // immediate failure
            if (res_cb) {
                ActionResult res;
                res.success = false;
                res.message = "Failed to send command";
                res.final_position = current_position_;
                res.final_status = current_status_;
                res.alarm_code = current_alarm_;
                res_cb(res);
            }
            std::lock_guard<std::mutex> lk(action_mutex_);
            action_active_ = false;
            action_started_ = false;
            return;
        }

        // If the command is stop, consider it completed immediately
        if (action_req.command == "stop") {
            if (res_cb) {
                ActionResult res;
                res.success = true;
                res.message = "Brake command sent";
                res.final_position = current_position_;
                res.final_status = current_status_;
                res.alarm_code = current_alarm_;
                res_cb(res);
            }
            std::lock_guard<std::mutex> lk(action_mutex_);
            action_active_ = false;
            action_started_ = false;
            return;
        }
    }

    // Ongoing evaluation
    bool done = false;
    bool success = false;
    std::string message;

    if (action_req.command == "detect_base") {
        if ((current_status_ & (1 << 15)) != 0 || current_position_ <= kPositionTolerance * 10) {
            done = true; success = true; current_stage = Stage::BaseStop; message = "Base detected";
        }
    } else if (action_req.command == "lift_up") {
        if (/*(current_status_ & (1 << 10)) != 0 || */std::abs(current_position_ - action_req.target_position) <= kPositionTolerance) {
            done = true; success = true; current_stage = Stage::TopStop; message = "Target position reached";
        }
    } else if (action_req.command == "lift_down") {
        if (/*(current_status_ & (1 << 10)) != 0 || */std::abs(current_position_ - 0) <= kPositionTolerance) {
            done = true; success = true; current_stage = Stage::BaseStop; message = "Reached base";
        }
    } else if (action_req.command == "clear_alarm") {
        if (current_alarm_ == 0) { done = true; success = true; message = "Alarm cleared"; }
    }

    publish_feedback(current_stage);

    if (done) {
        if (res_cb) {
            ActionResult res;
            res.success = success;
            res.message = message.empty() ? (success ? "Succeeded" : "Failed") : message;
            res.final_position = current_position_;
            res.final_status = current_status_;
            res.alarm_code = current_alarm_;
            res_cb(res);
        }
        std::lock_guard<std::mutex> lk(action_mutex_);
        action_active_ = false;
        action_started_ = false;
        current_stage_ = current_stage;
        return;
    }
}

void JackOperator::set_position_callback(position_callback_t callback)
{
    position_callback_ = std::move(callback);
}

void JackOperator::set_status_callback(status_callback_t callback)
{
    status_callback_ = std::move(callback);
}

void JackOperator::set_alarm_callback(alarm_callback_t callback)
{
    alarm_callback_ = std::move(callback);
}

void JackOperator::set_stage_callback(stage_callback_t callback)
{
    stage_callback_ = std::move(callback);
}

bool JackOperator::start_action(const ActionRequest& req, feedback_callback_t fb, result_callback_t done)
{
    if (!jack_ctrl_ || !jack_ctrl_->is_open()) {
        return false;
    }
    std::lock_guard<std::mutex> lk(action_mutex_);
    if (action_active_) {
        return false;
    }
    action_req_ = req;
    feedback_cb_ = std::move(fb);
    result_cb_ = std::move(done);
    action_active_ = true;
    action_started_ = false;
    cancel_requested_ = false;
    // initial stage based on command
    if (req.command == "detect_base") current_stage_ = Stage::DetectingBase;
    else if (req.command == "lift_up") current_stage_ = Stage::LiftingUp;
    else if (req.command == "lift_down") current_stage_ = Stage::LiftingDown;
    else if (req.command == "stop") current_stage_ = Stage::MiddleStop;
    else current_stage_ = Stage::Init;

    return true;
}

void JackOperator::cancel_action()
{
    std::lock_guard<std::mutex> lk(action_mutex_);
    if (action_active_) {
        cancel_requested_ = true;
    }
}

bool JackOperator::cmd_brake()
{
    if (!jack_ctrl_ || !jack_ctrl_->is_open()) {
        return false;
    }
    return jack_ctrl_->cmd_brake();
}

bool JackOperator::cmd_detect_base()
{
    if (!jack_ctrl_ || !jack_ctrl_->is_open()) {
        return false;
    }

    if (!jack_ctrl_->set_mode_detect_base()) {
        return false;
    }
    return jack_ctrl_->cmd_detect_base();
}

bool JackOperator::cmd_clear_alarm()
{
    if (!jack_ctrl_ || !jack_ctrl_->is_open()) {
        return false;
    }
    return jack_ctrl_->cmd_clear_alarm();
}

bool JackOperator::cmd_lift_up(std::int32_t target_position, std::uint32_t speed)
{
    if (!jack_ctrl_ || !jack_ctrl_->is_open()) {
        return false;
    }

    std::uint32_t can_speed = slpmu::JackControllerCan::rpm_to_can_speed(speed);
    if (!jack_ctrl_->write_target_speed(can_speed)) {
        return false;
    }

    if (!jack_ctrl_->write_target_position(target_position)) {
        return false;
    }

    if (!jack_ctrl_->set_mode_position()) {
        return false;
    }

    if (!jack_ctrl_->cmd_auto_pos_change()) {
        return false;
    }
    return true;
}

bool JackOperator::cmd_lift_down(std::uint32_t speed)
{
    if (!jack_ctrl_ || !jack_ctrl_->is_open()) {
        return false;
    }

    std::uint32_t can_speed = slpmu::JackControllerCan::rpm_to_can_speed(speed);
    if (!jack_ctrl_->write_target_speed(can_speed)) {
        return false;
    }

    if (!jack_ctrl_->write_target_position(0)) {
        return false;
    }

    if (!jack_ctrl_->set_mode_position()) {
        return false;
    }

    if (!jack_ctrl_->cmd_auto_pos_change()) {
        return false;
    }
    return true;
}

}
