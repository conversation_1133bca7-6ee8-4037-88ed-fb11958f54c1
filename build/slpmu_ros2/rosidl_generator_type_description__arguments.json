{"package_name": "slpmu_ros2", "output_dir": "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_type_description/slpmu_ros2", "idl_tuples": ["/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_adapter/slpmu_ros2:action/JackControl.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_adapter/slpmu_ros2:msg/JackStatus.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_adapter/slpmu_ros2:msg/MotorState.idl"], "include_paths": ["std_msgs:/opt/ros/jazzy/share/std_msgs", "builtin_interfaces:/opt/ros/jazzy/share/builtin_interfaces", "service_msgs:/opt/ros/jazzy/share/service_msgs", "action_msgs:/opt/ros/jazzy/share/action_msgs", "unique_identifier_msgs:/opt/ros/jazzy/share/unique_identifier_msgs"]}