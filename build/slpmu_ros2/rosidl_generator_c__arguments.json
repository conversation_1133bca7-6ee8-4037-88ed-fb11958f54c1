{"package_name": "slpmu_ros2", "output_dir": "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_c/slpmu_ros2", "template_dir": "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource", "idl_tuples": ["/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_adapter/slpmu_ros2:action/JackControl.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_adapter/slpmu_ros2:msg/JackStatus.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_adapter/slpmu_ros2:msg/MotorState.idl"], "ros_interface_files": ["/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/action/JackControl.action", "/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/msg/JackStatus.msg", "/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/msg/MotorState.msg"], "ros_interface_dependencies": ["std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Bool.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Byte.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/ByteMultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Char.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/ColorRGBA.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Empty.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Float32.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Float32MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Float64.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Float64MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Header.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int16.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int16MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int32.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int32MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int64.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int64MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int8.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/Int8MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/MultiArrayDimension.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/MultiArrayLayout.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/String.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt16.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt16MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt32.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt32MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt64.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt64MultiArray.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt8.idl", "std_msgs:/opt/ros/jazzy/share/std_msgs/msg/UInt8MultiArray.idl", "builtin_interfaces:/opt/ros/jazzy/share/builtin_interfaces/msg/Duration.idl", "builtin_interfaces:/opt/ros/jazzy/share/builtin_interfaces/msg/Time.idl", "service_msgs:/opt/ros/jazzy/share/service_msgs/msg/ServiceEventInfo.idl", "action_msgs:/opt/ros/jazzy/share/action_msgs/msg/GoalInfo.idl", "action_msgs:/opt/ros/jazzy/share/action_msgs/msg/GoalStatus.idl", "action_msgs:/opt/ros/jazzy/share/action_msgs/msg/GoalStatusArray.idl", "action_msgs:/opt/ros/jazzy/share/action_msgs/srv/CancelGoal.idl", "unique_identifier_msgs:/opt/ros/jazzy/share/unique_identifier_msgs/msg/UUID.idl"], "target_dependencies": ["/opt/ros/jazzy/share/rosidl_generator_c/cmake/../../../lib/rosidl_generator_c/rosidl_generator_c", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../../../lib/python3.12/site-packages/rosidl_generator_c/__init__.py", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/action__type_support.h.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/action__type_support.c.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/empty__description.c.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/full__description.c.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/idl.h.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/idl__description.c.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/idl__functions.c.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/idl__functions.h.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/idl__struct.h.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/idl__type_support.c.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/idl__type_support.h.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/msg__functions.c.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/msg__functions.h.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/msg__struct.h.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/msg__type_support.h.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/srv__type_support.c.em", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/../resource/srv__type_support.h.em", "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_adapter/slpmu_ros2/action/JackControl.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_adapter/slpmu_ros2/msg/JackStatus.idl", "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_adapter/slpmu_ros2/msg/MotorState.idl", "/opt/ros/jazzy/share/std_msgs/msg/Bool.idl", "/opt/ros/jazzy/share/std_msgs/msg/Byte.idl", "/opt/ros/jazzy/share/std_msgs/msg/ByteMultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Char.idl", "/opt/ros/jazzy/share/std_msgs/msg/ColorRGBA.idl", "/opt/ros/jazzy/share/std_msgs/msg/Empty.idl", "/opt/ros/jazzy/share/std_msgs/msg/Float32.idl", "/opt/ros/jazzy/share/std_msgs/msg/Float32MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Float64.idl", "/opt/ros/jazzy/share/std_msgs/msg/Float64MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Header.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int16.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int16MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int32.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int32MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int64.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int64MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int8.idl", "/opt/ros/jazzy/share/std_msgs/msg/Int8MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/MultiArrayDimension.idl", "/opt/ros/jazzy/share/std_msgs/msg/MultiArrayLayout.idl", "/opt/ros/jazzy/share/std_msgs/msg/String.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt16.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt16MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt32.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt32MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt64.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt64MultiArray.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt8.idl", "/opt/ros/jazzy/share/std_msgs/msg/UInt8MultiArray.idl", "/opt/ros/jazzy/share/builtin_interfaces/msg/Duration.idl", "/opt/ros/jazzy/share/builtin_interfaces/msg/Time.idl", "/opt/ros/jazzy/share/service_msgs/msg/ServiceEventInfo.idl", "/opt/ros/jazzy/share/action_msgs/msg/GoalInfo.idl", "/opt/ros/jazzy/share/action_msgs/msg/GoalStatus.idl", "/opt/ros/jazzy/share/action_msgs/msg/GoalStatusArray.idl", "/opt/ros/jazzy/share/action_msgs/srv/CancelGoal.idl", "/opt/ros/jazzy/share/unique_identifier_msgs/msg/UUID.idl"], "type_description_tuples": ["action/JackControl.idl:/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_type_description/slpmu_ros2/action/JackControl.json", "msg/JackStatus.idl:/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_type_description/slpmu_ros2/msg/JackStatus.json", "msg/MotorState.idl:/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_type_description/slpmu_ros2/msg/MotorState.json"]}