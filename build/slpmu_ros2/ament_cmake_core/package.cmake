set(_AMENT_PACKAGE_NAME "slpmu_ros2")
set(slpmu_ros2_VERSION "1.0.0")
set(slpmu_ros2_MAINTAINER "<PERSON> <<EMAIL>>")
set(slpmu_ros2_BUILD_DEPENDS "rosidl_default_generators" "rclcpp" "rclcpp_action" "sensor_msgs" "geometry_msgs" "std_msgs" "nav_msgs" "rclcpp_components" "slcan" "slpmu_motor" "slpmu_process" "slpmu_jack")
set(slpmu_ros2_BUILDTOOL_DEPENDS "ament_cmake")
set(slpmu_ros2_BUILD_EXPORT_DEPENDS "rclcpp" "rclcpp_action" "sensor_msgs" "geometry_msgs" "std_msgs" "nav_msgs" "rclcpp_components" "slcan" "slpmu_motor" "slpmu_process" "slpmu_jack")
set(slpmu_ros2_BUILDTOOL_EXPORT_DEPENDS )
set(slpmu_ros2_EXEC_DEPENDS "rosidl_default_runtime" "launch" "rclcpp" "rclcpp_action" "sensor_msgs" "geometry_msgs" "std_msgs" "nav_msgs" "rclcpp_components" "slcan" "slpmu_motor" "slpmu_process" "slpmu_jack")
set(slpmu_ros2_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(slpmu_ros2_GROUP_DEPENDS )
set(slpmu_ros2_MEMBER_OF_GROUPS "rosidl_interface_packages")
set(slpmu_ros2_DEPRECATED "")
set(slpmu_ros2_EXPORT_TAGS)
list(APPEND slpmu_ros2_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
