file(REMOVE_RECURSE
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_c.c"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_fastrtps_c.c"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_introspection_c.c"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/action/__init__.py"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/action/_jack_control.py"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/action/_jack_control_s.c"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/msg/__init__.py"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/msg/_jack_status.py"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/msg/_jack_status_s.c"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/msg/_motor_state.py"
  "/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2/rosidl_generator_py/slpmu_ros2/msg/_motor_state_s.c"
  "CMakeFiles/slpmu_ros2__py"
)

# Per-language clean rules from dependency scanning.
foreach(lang )
  include(CMakeFiles/slpmu_ros2__py.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
