[ 25%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[ 50%] [32m[1mLinking CXX executable slpmu_node[0m
[100%] Built target slpmu_node
-- Install configuration: "Release"
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/slpmu_ros2/slpmu_node
-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/slpmu_ros2/slpmu_node" to ""
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/launch
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/launch/slpmu_ros2.launch.py
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/config
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/config/slpmu_ros2.yaml
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/package_run_dependencies/slpmu_ros2
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/parent_prefix_path/slpmu_ros2
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/path.sh
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/path.dsv
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.bash
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.sh
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.zsh
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.dsv
-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/packages/slpmu_ros2
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2Config.cmake
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2Config-version.cmake
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.xml
