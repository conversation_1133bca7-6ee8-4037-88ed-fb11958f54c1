[0.000000] (-) TimerEvent: {}
[0.000940] (sl_vcu_all) JobQueued: {'identifier': 'sl_vcu_all', 'dependencies': OrderedDict()}
[0.001077] (slcan) JobQueued: {'identifier': 'slcan', 'dependencies': OrderedDict()}
[0.001243] (slpmu_process) JobQueued: {'identifier': 'slpmu_process', 'dependencies': OrderedDict()}
[0.001270] (slpmu_jack) JobQueued: {'identifier': 'slpmu_jack', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001307] (slpmu_motor) JobQueued: {'identifier': 'slpmu_motor', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001340] (slpmu_power) JobQueued: {'identifier': 'slpmu_power', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001373] (slpmu_ros2) JobQueued: {'identifier': 'slpmu_ros2', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan', 'slpmu_process': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process', 'slpmu_motor': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor'})}
[0.001398] (slcan) JobStarted: {'identifier': 'slcan'}
[0.008115] (slpmu_process) JobStarted: {'identifier': 'slpmu_process'}
[0.012467] (sl_vcu_all) JobStarted: {'identifier': 'sl_vcu_all'}
[0.017876] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'cmake'}
[0.018597] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'build'}
[0.019507] (slcan) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.021846] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'cmake'}
[0.022587] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'build'}
[0.022932] (slpmu_process) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.026965] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'cmake'}
[0.027820] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'build'}
[0.028430] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.088288] (slpmu_process) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_process.dir/src/imu_odom_fusion.cpp.o\x1b[0m\n'}
[0.092035] (slcan) StdoutLine: {'line': b'[100%] Built target slcan\n'}
[0.101265] (-) TimerEvent: {}
[0.107032] (slcan) CommandEnded: {'returncode': 0}
[0.107662] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'install'}
[0.108192] (sl_vcu_all) StdoutLine: {'line': b'[  0%] Built target sl_vcu_all__rosidl_generator_type_description\n'}
[0.127967] (slcan) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.142514] (slcan) StdoutLine: {'line': b'-- Install configuration: "RelWithDebInfo"\n'}
[0.142753] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a\n'}
[0.142924] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include\n'}
[0.143128] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan\n'}
[0.143236] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan.hpp\n'}
[0.143328] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan\n'}
[0.143417] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/canopen.hpp\n'}
[0.143507] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/socket_can.hpp\n'}
[0.143595] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets.cmake\n'}
[0.143682] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets-relwithdebinfo.cmake\n'}
[0.144273] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/slcan-config.cmake\n'}
[0.144877] (slcan) CommandEnded: {'returncode': 0}
[0.155233] (sl_vcu_all) StdoutLine: {'line': b'[  1%] Built target sl_vcu_all__cpp\n'}
[0.160447] (sl_vcu_all) StdoutLine: {'line': b'[  1%] Built target ament_cmake_python_copy_sl_vcu_all\n'}
[0.160589] (sl_vcu_all) StdoutLine: {'line': b'[ 25%] Built target sl_vcu_all__rosidl_generator_c\n'}
[0.184977] (slcan) JobEnded: {'identifier': 'slcan', 'rc': 0}
[0.185487] (slpmu_motor) JobStarted: {'identifier': 'slpmu_motor'}
[0.197210] (slpmu_jack) JobStarted: {'identifier': 'slpmu_jack'}
[0.202441] (-) TimerEvent: {}
[0.215951] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'cmake'}
[0.217390] (sl_vcu_all) StdoutLine: {'line': b'[ 34%] Built target sl_vcu_all__rosidl_typesupport_cpp\n'}
[0.218829] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'build'}
[0.220307] (slpmu_motor) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.231294] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'cmake'}
[0.234798] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'build'}
[0.234855] (slpmu_jack) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.269218] (sl_vcu_all) StdoutLine: {'line': b'[ 42%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_c\n'}
[0.269467] (sl_vcu_all) StdoutLine: {'line': b'[ 51%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_cpp\n'}
[0.302536] (-) TimerEvent: {}
[0.306080] (sl_vcu_all) StdoutLine: {'line': b'[ 59%] Built target sl_vcu_all__rosidl_typesupport_introspection_c\n'}
[0.320045] (sl_vcu_all) StdoutLine: {'line': b'[ 68%] Built target sl_vcu_all__rosidl_typesupport_c\n'}
[0.332335] (slpmu_motor) StdoutLine: {'line': b'[ 50%] Built target slpmu_motor\n'}
[0.349253] (slpmu_jack) StdoutLine: {'line': b'[ 50%] Built target slpmu_jack\n'}
[0.355234] (sl_vcu_all) StdoutLine: {'line': b'[ 70%] Built target can_frame_dispatcher_node\n'}
[0.357028] (sl_vcu_all) StdoutLine: {'line': b'[ 78%] Built target sl_vcu_all__rosidl_typesupport_introspection_cpp\n'}
[0.382207] (slpmu_motor) StdoutLine: {'line': b'[100%] Built target motor_utils\n'}
[0.402631] (-) TimerEvent: {}
[0.408591] (slpmu_motor) CommandEnded: {'returncode': 0}
[0.408984] (sl_vcu_all) StdoutLine: {'line': b'[ 79%] Built target zl_motor_controller_node\n'}
[0.410319] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'install'}
[0.410380] (slpmu_jack) StdoutLine: {'line': b'[100%] Built target jack_utils\n'}
[0.410886] (slpmu_motor) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.431655] (sl_vcu_all) StdoutLine: {'line': b'[ 81%] Built target zl_motor_modbus_controller_node\n'}
[0.431871] (sl_vcu_all) StdoutLine: {'line': b'[ 82%] Built target bumper_sensor_node\n'}
[0.437774] (slpmu_motor) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.438090] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/libslpmu_motor.a\n'}
[0.438274] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include\n'}
[0.438436] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu\n'}
[0.438533] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor\n'}
[0.438626] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/i_motor_controller.hpp\n'}
[0.439505] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/motor_controller_can.hpp\n'}
[0.439657] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/slpmu_motor.hpp\n'}
[0.439761] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets.cmake\n'}
[0.439855] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets-release.cmake\n'}
[0.440068] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/slpmu_motor-config.cmake\n'}
[0.441338] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin/motor_utils\n'}
[0.443255] (slpmu_motor) CommandEnded: {'returncode': 0}
[0.478208] (slpmu_motor) JobEnded: {'identifier': 'slpmu_motor', 'rc': 0}
[0.478659] (slpmu_power) JobStarted: {'identifier': 'slpmu_power'}
[0.485469] (slpmu_jack) CommandEnded: {'returncode': 0}
[0.486159] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'install'}
[0.486774] (slpmu_jack) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.487214] (sl_vcu_all) StdoutLine: {'line': b'[ 83%] Built target imu_sensor_node\n'}
[0.495301] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'cmake'}
[0.495359] (sl_vcu_all) StdoutLine: {'line': b'[ 84%] Built target battery_monitor_node\n'}
[0.499628] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'build'}
[0.499689] (slpmu_power) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.503485] (-) TimerEvent: {}
[0.505989] (sl_vcu_all) StdoutLine: {'line': b'[ 85%] Built target teleop_key\n'}
[0.522859] (slpmu_jack) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.523323] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin/jack_utils\n'}
[0.523737] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/libslpmu_jack.a\n'}
[0.524139] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include\n'}
[0.524547] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu\n'}
[0.526064] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack\n'}
[0.527974] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/i_jack_controller.hpp\n'}
[0.528755] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp\n'}
[0.529690] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/slpmu_jack.hpp\n'}
[0.530146] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets.cmake\n'}
[0.530934] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets-release.cmake\n'}
[0.531782] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/slpmu_jack-config.cmake\n'}
[0.532226] (slpmu_jack) CommandEnded: {'returncode': 0}
[0.563261] (slpmu_jack) JobEnded: {'identifier': 'slpmu_jack', 'rc': 0}
[0.564006] (sl_vcu_all) StdoutLine: {'line': b'[ 87%] Built target led_display_control_node\n'}
[0.564860] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target jack_control_node\n'}
[0.569271] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target sl_vcu_all\n'}
[0.603575] (-) TimerEvent: {}
[0.634716] (sl_vcu_all) StdoutLine: {'line': b'running egg_info\n'}
[0.636188] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target sl_vcu_all__py\n'}
[0.650934] (slpmu_power) StdoutLine: {'line': b'[ 50%] Built target slpmu_power\n'}
[0.670708] (sl_vcu_all) StdoutLine: {'line': b'writing sl_vcu_all.egg-info/PKG-INFO\n'}
[0.671277] (sl_vcu_all) StdoutLine: {'line': b'writing dependency_links to sl_vcu_all.egg-info/dependency_links.txt\n'}
[0.671747] (sl_vcu_all) StdoutLine: {'line': b'writing top-level names to sl_vcu_all.egg-info/top_level.txt\n'}
[0.676631] (sl_vcu_all) StdoutLine: {'line': b'[ 96%] Built target sl_vcu_all__rosidl_generator_py\n'}
[0.786560] (-) TimerEvent: {}
[0.788290] (sl_vcu_all) StdoutLine: {'line': b'[ 98%] Built target sl_vcu_all_s__rosidl_typesupport_fastrtps_c\n'}
[0.788481] (sl_vcu_all) StdoutLine: {'line': b'[ 99%] Built target sl_vcu_all_s__rosidl_typesupport_introspection_c\n'}
[0.789411] (slpmu_power) StdoutLine: {'line': b'[100%] Built target power_utils\n'}
[0.790336] (slpmu_power) CommandEnded: {'returncode': 0}
[0.790752] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'install'}
[0.790782] (slpmu_power) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.793256] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target sl_vcu_all_s__rosidl_typesupport_c\n'}
[0.802508] (slpmu_power) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.802961] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/libslpmu_power.a\n'}
[0.803527] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include\n'}
[0.803635] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu\n'}
[0.803732] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/slpmu_power.hpp\n'}
[0.803826] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power\n'}
[0.803932] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/i_battery.hpp\n'}
[0.804025] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/battery_can.hpp\n'}
[0.805425] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets.cmake\n'}
[0.805599] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets-noconfig.cmake\n'}
[0.805704] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/slpmu_power-config.cmake\n'}
[0.805793] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin/power_utils\n'}
[0.807229] (slpmu_power) CommandEnded: {'returncode': 0}
[0.826017] (slpmu_power) JobEnded: {'identifier': 'slpmu_power', 'rc': 0}
[0.851083] (sl_vcu_all) StdoutLine: {'line': b"reading manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[0.852851] (sl_vcu_all) StdoutLine: {'line': b"writing manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[0.886705] (-) TimerEvent: {}
[0.892778] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_sl_vcu_all_egg\n'}
[0.912552] (sl_vcu_all) CommandEnded: {'returncode': 0}
[0.913378] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'install'}
[0.914103] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.926399] (sl_vcu_all) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.926589] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/rosidl_interfaces/sl_vcu_all\n'}
[0.926991] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.json\n'}
[0.927467] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.json\n'}
[0.927794] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.json\n'}
[0.928094] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.json\n'}
[0.928412] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.json\n'}
[0.928716] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.json\n'}
[0.929018] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.json\n'}
[0.929429] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.json\n'}
[0.929829] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.json\n'}
[0.930145] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.json\n'}
[0.930448] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.json\n'}
[0.930758] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.json\n'}
[0.931076] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.931313] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.931406] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.932097] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.h\n'}
[0.932256] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.h\n'}
[0.932347] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.h\n'}
[0.932466] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.c\n'}
[0.932715] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[0.932961] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__description.c\n'}
[0.933052] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.h\n'}
[0.933145] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.933250] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.h\n'}
[0.933340] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.933427] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.h\n'}
[0.933514] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.h\n'}
[0.933603] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.c\n'}
[0.933691] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.h\n'}
[0.933856] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[0.934057] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[0.934241] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.h\n'}
[0.934418] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__description.c\n'}
[0.934541] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.h\n'}
[0.934851] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.c\n'}
[0.935086] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[0.935233] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.c\n'}
[0.935396] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__description.c\n'}
[0.935520] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__description.c\n'}
[0.935677] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.h\n'}
[0.935793] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.h\n'}
[0.935986] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.h\n'}
[0.936090] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.h\n'}
[0.936192] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.h\n'}
[0.936349] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.h\n'}
[0.936544] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.936823] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.h\n'}
[0.936950] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.h\n'}
[0.937067] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.h\n'}
[0.937335] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.h\n'}
[0.937612] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_c__visibility_control.h\n'}
[0.937718] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.h\n'}
[0.937859] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.937947] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.h\n'}
[0.938049] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__description.c\n'}
[0.938164] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[0.938262] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__description.c\n'}
[0.938397] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.c\n'}
[0.938590] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.c\n'}
[0.938804] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.h\n'}
[0.939023] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[0.939158] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.h\n'}
[0.939259] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[0.939377] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.h\n'}
[0.939463] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.h\n'}
[0.939546] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.h\n'}
[0.939671] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.c\n'}
[0.939860] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.h\n'}
[0.939948] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[0.940032] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[0.940116] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.h\n'}
[0.940208] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[0.940298] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__description.c\n'}
[0.940381] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__description.c\n'}
[0.940464] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.h\n'}
[0.940548] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.h\n'}
[0.940630] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.h\n'}
[0.940713] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.c\n'}
[0.940805] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.h\n'}
[0.940899] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.h\n'}
[0.940990] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.h\n'}
[0.941073] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.h\n'}
[0.941155] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[0.941636] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[0.941899] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__description.c\n'}
[0.942141] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.h\n'}
[0.942528] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.c\n'}
[0.942673] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.h\n'}
[0.942764] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.c\n'}
[0.942869] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__description.c\n'}
[0.942986] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.h\n'}
[0.943072] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__description.c\n'}
[0.943185] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.c\n'}
[0.943429] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__description.c\n'}
[0.943524] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.h\n'}
[0.943613] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.h\n'}
[0.943820] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.h\n'}
[0.943917] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.h\n'}
[0.944058] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.c\n'}
[0.944223] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.h\n'}
[0.944325] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.h\n'}
[0.944574] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.h\n'}
[0.944708] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.h\n'}
[0.944842] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.h\n'}
[0.944983] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.sh\n'}
[0.945148] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.dsv\n'}
[0.945258] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_c.so\n'}
[0.945380] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.945524] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.945664] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.945818] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_c.h\n'}
[0.945932] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.946118] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.946215] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_c.h\n'}
[0.946347] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_c.h\n'}
[0.946437] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.946529] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.946621] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.946751] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.946841] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_c.h\n'}
[0.946948] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.947213] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.947530] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.947865] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_c.h\n'}
[0.948026] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_c.h\n'}
[0.948116] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_c.h\n'}
[0.948212] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.948363] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_c.so\n'}
[0.948452] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.948543] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.948630] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.948741] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__builder.hpp\n'}
[0.948830] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__traits.hpp\n'}
[0.948921] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.hpp\n'}
[0.949099] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.hpp\n'}
[0.949249] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.hpp\n'}
[0.949355] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.949479] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.hpp\n'}
[0.949619] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.949772] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.hpp\n'}
[0.949922] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__builder.hpp\n'}
[0.950222] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__builder.hpp\n'}
[0.950454] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.hpp\n'}
[0.950674] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__builder.hpp\n'}
[0.950948] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.hpp\n'}
[0.951105] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__traits.hpp\n'}
[0.951206] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__traits.hpp\n'}
[0.951299] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.hpp\n'}
[0.951384] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__traits.hpp\n'}
[0.951488] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.hpp\n'}
[0.951578] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.hpp\n'}
[0.951662] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.hpp\n'}
[0.951747] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.hpp\n'}
[0.951860] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.951944] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.hpp\n'}
[0.952027] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.hpp\n'}
[0.952108] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.hpp\n'}
[0.952202] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.hpp\n'}
[0.952292] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.952375] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__traits.hpp\n'}
[0.952458] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.hpp\n'}
[0.952541] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__builder.hpp\n'}
[0.952623] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__traits.hpp\n'}
[0.952704] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__traits.hpp\n'}
[0.952787] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.hpp\n'}
[0.952873] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__builder.hpp\n'}
[0.952966] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__traits.hpp\n'}
[0.953051] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__builder.hpp\n'}
[0.953134] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.hpp\n'}
[0.953243] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__traits.hpp\n'}
[0.953332] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__traits.hpp\n'}
[0.953415] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__builder.hpp\n'}
[0.953498] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__builder.hpp\n'}
[0.953581] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__traits.hpp\n'}
[0.953663] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.hpp\n'}
[0.953749] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.hpp\n'}
[0.953833] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.hpp\n'}
[0.953917] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.hpp\n'}
[0.954006] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.hpp\n'}
[0.954090] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.hpp\n'}
[0.954188] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__builder.hpp\n'}
[0.954288] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.hpp\n'}
[0.954372] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__traits.hpp\n'}
[0.954454] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.hpp\n'}
[0.954537] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.hpp\n'}
[0.954620] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.hpp\n'}
[0.954702] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.hpp\n'}
[0.954793] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.hpp\n'}
[0.954876] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__builder.hpp\n'}
[0.954958] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__builder.hpp\n'}
[0.955040] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.hpp\n'}
[0.955122] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.hpp\n'}
[0.955219] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.hpp\n'}
[0.955309] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.hpp\n'}
[0.955391] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.955474] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.hpp\n'}
[0.955557] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.955643] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.955726] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.955808] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/dds_fastrtps\n'}
[0.955904] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.955988] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.956071] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.956154] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/dds_fastrtps\n'}
[0.956251] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.956337] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.956427] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.956511] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.956593] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.956682] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/dds_fastrtps\n'}
[0.956765] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.956849] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.956933] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.957020] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.957104] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.957295] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.957405] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.957490] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.957573] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.957655] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.957737] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.957819] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.957900] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.957983] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_c.h\n'}
[0.958065] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[0.958149] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.958249] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.958334] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_c.h\n'}
[0.958432] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[0.958518] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[0.958606] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_c.h\n'}
[0.958688] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[0.958776] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_c.h\n'}
[0.958859] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.958942] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.959024] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.959107] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[0.959200] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_c.h\n'}
[0.959289] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_c.h\n'}
[0.959370] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[0.959451] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[0.959532] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_c.h\n'}
[0.959614] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_c.h\n'}
[0.959696] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[0.959779] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[0.959864] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[0.959946] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[0.960027] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[0.960108] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_c.h\n'}
[0.962228] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_c.h\n'}
[0.962435] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_c.h\n'}
[0.962536] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_c.h\n'}
[0.962623] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_c.so\n'}
[0.962708] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_c.so\n'}
[0.962792] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.962875] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.962970] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.963052] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.963136] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.cpp\n'}
[0.963242] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.963332] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.963418] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.cpp\n'}
[0.963504] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.cpp\n'}
[0.963588] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.963673] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.cpp\n'}
[0.963755] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.963837] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.963918] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.964000] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.964082] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.cpp\n'}
[0.964165] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.967328] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.cpp\n'}
[0.967510] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.cpp\n'}
[0.967598] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.cpp\n'}
[0.967681] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.967764] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.967848] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.967932] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.968016] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.968097] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.cpp\n'}
[0.970199] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.cpp\n'}
[0.970338] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.970427] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.cpp\n'}
[0.970511] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.970611] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.cpp\n'}
[0.970698] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_cpp.so\n'}
[0.970782] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_cpp.so\n'}
[0.970865] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.sh\n'}
[0.970947] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.dsv\n'}
[0.971031] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info\n'}
[0.971114] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/SOURCES.txt\n'}
[0.971210] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/PKG-INFO\n'}
[0.971301] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/top_level.txt\n'}
[0.971390] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/dependency_links.txt\n'}
[0.971471] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all\n'}
[0.971554] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.971638] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[0.971721] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_c.c\n'}
[0.971805] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action\n'}
[0.971888] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/__init__.py\n'}
[0.971972] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control.py\n'}
[0.972056] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control_s.c\n'}
[0.972138] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.977743] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/__init__.py\n'}
[0.977851] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv\n'}
[0.977939] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status_s.c\n'}
[0.978024] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/__init__.py\n'}
[0.978109] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control.py\n'}
[0.981262] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control_s.c\n'}
[0.981379] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status.py\n'}
[0.981465] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter.py\n'}
[0.981550] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter_s.c\n'}
[0.981633] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[0.981741] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg\n'}
[0.981831] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info.py\n'}
[0.981915] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame_s.c\n'}
[0.981997] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data_s.c\n'}
[0.982081] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info_s.c\n'}
[0.982165] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data.py\n'}
[0.982265] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status_s.c\n'}
[0.982352] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status.py\n'}
[0.982434] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state_s.c\n'}
[0.982518] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/__init__.py\n'}
[0.982601] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status_s.c\n'}
[0.984284] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state.py\n'}
[0.984390] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status.py\n'}
[0.984475] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame.py\n'}
[0.984560] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state.py\n'}
[0.984643] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data.py\n'}
[0.984726] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state_s.c\n'}
[0.984809] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data_s.c\n'}
[0.984892] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[0.987202] (-) TimerEvent: {}
[0.995873] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all'...\n"}
[0.996054] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action'...\n"}
[0.996151] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg'...\n"}
[0.997355] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv'...\n"}
[1.005514] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[1.005977] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[1.006405] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[1.009210] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_py.so\n'}
[1.009509] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.idl\n'}
[1.009692] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.idl\n'}
[1.009899] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.idl\n'}
[1.010092] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.idl\n'}
[1.010214] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.idl\n'}
[1.010301] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.idl\n'}
[1.010385] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.idl\n'}
[1.010468] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.idl\n'}
[1.010583] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.idl\n'}
[1.010775] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.idl\n'}
[1.010896] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.idl\n'}
[1.011014] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.idl\n'}
[1.011155] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.srv\n'}
[1.011378] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.srv\n'}
[1.011482] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.srv\n'}
[1.011585] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.msg\n'}
[1.011676] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.msg\n'}
[1.011940] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.msg\n'}
[1.012046] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.msg\n'}
[1.012143] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.msg\n'}
[1.012244] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.msg\n'}
[1.012342] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.msg\n'}
[1.012425] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.msg\n'}
[1.013332] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.action\n'}
[1.013487] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/can_frame_dispatcher_node\n'}
[1.013763] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_controller_node\n'}
[1.013862] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_modbus_controller_node\n'}
[1.017442] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/bumper_sensor_node\n'}
[1.018217] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/imu_sensor_node\n'}
[1.018721] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/teleop_key\n'}
[1.019406] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/battery_monitor_node\n'}
[1.019899] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/jack_control_node\n'}
[1.020420] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/led_display_control_node\n'}
[1.021478] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch\n'}
[1.022304] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/battery_monitor.launch.py\n'}
[1.022902] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/jack_control.launch.py\n'}
[1.023819] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/robot_localization_ekf.launch.py\n'}
[1.023975] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_controller.launch.py\n'}
[1.024088] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_modbus_controller.launch.py\n'}
[1.024199] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/led_control.launch.py\n'}
[1.024362] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/vcu_nodes.launch.py\n'}
[1.024460] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/imu_sensor.launch.py\n'}
[1.024931] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/bumper_sensor.launch.py\n'}
[1.025053] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/sl_vcu_all.launch.py\n'}
[1.025287] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config\n'}
[1.025527] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/ekf.yaml\n'}
[1.025857] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_modbus_controller.yaml\n'}
[1.026297] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/battery_monitor.yaml\n'}
[1.026651] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_controller.yaml\n'}
[1.026833] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/jack_control.yaml\n'}
[1.027282] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/imu_sensor.yaml\n'}
[1.027585] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/robot_localization_ekf.yaml\n'}
[1.028425] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/bumper_sensor.yaml\n'}
[1.028586] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/led_control.yaml\n'}
[1.029005] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/package_run_dependencies/sl_vcu_all\n'}
[1.029108] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/parent_prefix_path/sl_vcu_all\n'}
[1.029206] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.sh\n'}
[1.029294] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.dsv\n'}
[1.029380] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.sh\n'}
[1.029465] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.dsv\n'}
[1.029549] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.bash\n'}
[1.029633] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.sh\n'}
[1.029717] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.zsh\n'}
[1.029801] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.dsv\n'}
[1.029885] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv\n'}
[1.029969] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/packages/sl_vcu_all\n'}
[1.030054] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport.cmake\n'}
[1.030149] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport-noconfig.cmake\n'}
[1.030252] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[1.030345] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[1.030438] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cppExport.cmake\n'}
[1.030525] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[1.030616] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[1.030719] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport.cmake\n'}
[1.030808] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[1.030894] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport.cmake\n'}
[1.030983] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport-noconfig.cmake\n'}
[1.031067] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport.cmake\n'}
[1.031160] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[1.033381] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport.cmake\n'}
[1.033595] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[1.033721] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport.cmake\n'}
[1.033816] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport-noconfig.cmake\n'}
[1.033912] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake-extras.cmake\n'}
[1.033999] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[1.034084] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[1.034170] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[1.034263] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_targets-extras.cmake\n'}
[1.034347] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[1.034444] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[1.034529] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig.cmake\n'}
[1.034611] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig-version.cmake\n'}
[1.034701] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.xml\n'}
[1.037712] (sl_vcu_all) CommandEnded: {'returncode': 0}
[1.041632] (slpmu_process) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX static library libslpmu_process.a\x1b[0m\n'}
[1.059910] (sl_vcu_all) JobEnded: {'identifier': 'sl_vcu_all', 'rc': 0}
[1.083934] (slpmu_process) StdoutLine: {'line': b'[100%] Built target slpmu_process\n'}
[1.087397] (-) TimerEvent: {}
[1.104297] (slpmu_process) CommandEnded: {'returncode': 0}
[1.104622] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'install'}
[1.104651] (slpmu_process) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[1.119862] (slpmu_process) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[1.120619] (slpmu_process) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/libslpmu_process.a\n'}
[1.120946] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include\n'}
[1.121272] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu\n'}
[1.121392] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/process\n'}
[1.121749] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/process/imu_odom_fusion.hpp\n'}
[1.121873] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/slpmu_process.hpp\n'}
[1.122405] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/cmake/slpmu_process_targets.cmake\n'}
[1.122543] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/cmake/slpmu_process_targets-release.cmake\n'}
[1.122643] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/slpmu_process-config.cmake\n'}
[1.126265] (slpmu_process) CommandEnded: {'returncode': 0}
[1.138190] (slpmu_process) JobEnded: {'identifier': 'slpmu_process', 'rc': 0}
[1.141478] (slpmu_ros2) JobStarted: {'identifier': 'slpmu_ros2'}
[1.151125] (slpmu_ros2) JobProgress: {'identifier': 'slpmu_ros2', 'progress': 'cmake'}
[1.152082] (slpmu_ros2) JobProgress: {'identifier': 'slpmu_ros2', 'progress': 'build'}
[1.152473] (slpmu_ros2) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[1.187490] (-) TimerEvent: {}
[1.188636] (slpmu_ros2) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[1.287598] (-) TimerEvent: {}
[1.320479] (slpmu_ros2) StdoutLine: {'line': b'-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)\n'}
[1.358287] (slpmu_ros2) StdoutLine: {'line': b'-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)\n'}
[1.370735] (slpmu_ros2) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)\n'}
[1.386095] (slpmu_ros2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.387703] (-) TimerEvent: {}
[1.403987] (slpmu_ros2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.487799] (-) TimerEvent: {}
[1.488334] (slpmu_ros2) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)\n'}
[1.490350] (slpmu_ros2) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)\n'}
[1.577569] (slpmu_ros2) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.587916] (-) TimerEvent: {}
[1.666520] (slpmu_ros2) StdoutLine: {'line': b'-- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)\n'}
[1.684674] (slpmu_ros2) StdoutLine: {'line': b'-- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)\n'}
[1.688355] (-) TimerEvent: {}
[1.704713] (slpmu_ros2) StdoutLine: {'line': b'-- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)\n'}
[1.740723] (slpmu_ros2) StdoutLine: {'line': b'-- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)\n'}
[1.745563] (slpmu_ros2) StdoutLine: {'line': b'-- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)\n'}
[1.790472] (-) TimerEvent: {}
[1.891648] (slpmu_ros2) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)\n'}
[1.891838] (-) TimerEvent: {}
[1.895896] (slpmu_ros2) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)\n'}
[1.897721] (slpmu_ros2) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[1.900120] (slpmu_ros2) StdoutLine: {'line': b'-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)\n'}
[1.935775] (slpmu_ros2) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)\n'}
[1.991956] (-) TimerEvent: {}
[2.055630] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[2.056101] (slpmu_ros2) StdoutLine: {'line': b'-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include>\n'}
[2.056248] (slpmu_ros2) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[2.056841] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[2.056955] (slpmu_ros2) StdoutLine: {'line': b"-- Configured 'flake8' exclude dirs and/or files: \n"}
[2.057245] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[2.057777] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[2.059535] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[2.059675] (slpmu_ros2) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[2.060192] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[2.061112] (slpmu_ros2) StdoutLine: {'line': b'-- Configuring done (0.9s)\n'}
[2.081840] (slpmu_ros2) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[2.088101] (slpmu_ros2) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2\n'}
[2.092048] (-) TimerEvent: {}
[2.157883] (slpmu_ros2) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o\x1b[0m\n'}
[2.158104] (slpmu_ros2) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o\x1b[0m\n'}
[2.158247] (slpmu_ros2) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o\x1b[0m\n'}
[2.193224] (-) TimerEvent: {}
[2.293484] (-) TimerEvent: {}
[2.393751] (-) TimerEvent: {}
[2.494133] (-) TimerEvent: {}
[2.596229] (-) TimerEvent: {}
[2.696649] (-) TimerEvent: {}
[2.798334] (-) TimerEvent: {}
[2.902194] (-) TimerEvent: {}
[3.002947] (-) TimerEvent: {}
[3.103354] (-) TimerEvent: {}
[3.204227] (-) TimerEvent: {}
[3.304513] (-) TimerEvent: {}
[3.404793] (-) TimerEvent: {}
[3.505173] (-) TimerEvent: {}
[3.605733] (-) TimerEvent: {}
[3.706271] (-) TimerEvent: {}
[3.808616] (-) TimerEvent: {}
[3.908883] (-) TimerEvent: {}
[4.009149] (-) TimerEvent: {}
[4.110461] (-) TimerEvent: {}
[4.210732] (-) TimerEvent: {}
[4.311236] (-) TimerEvent: {}
[4.412227] (-) TimerEvent: {}
[4.512674] (-) TimerEvent: {}
[4.614467] (-) TimerEvent: {}
[4.714845] (-) TimerEvent: {}
[4.816273] (-) TimerEvent: {}
[4.916858] (-) TimerEvent: {}
[5.017135] (-) TimerEvent: {}
[5.117603] (-) TimerEvent: {}
[5.217902] (-) TimerEvent: {}
[5.304143] (slpmu_ros2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_ros2_main.cpp:1\x1b[m\x1b[K:\n'}
[5.304378] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:26:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Ksl_vcu_all\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[5.304487] (slpmu_ros2) StderrLine: {'line': b'   79 |     rclcpp::Subscription<\x1b[01;31m\x1b[Ksl_vcu_all\x1b[m\x1b[K::msg::BumperState>::SharedPtr bumper_sub_;\n'}
[5.304584] (slpmu_ros2) StderrLine: {'line': b'      |                          \x1b[01;31m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[5.304754] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Ktemplate argument 1 is invalid\n'}
[5.304860] (slpmu_ros2) StderrLine: {'line': b'   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState\x1b[01;31m\x1b[K>\x1b[m\x1b[K::SharedPtr bumper_sub_;\n'}
[5.304954] (slpmu_ros2) StderrLine: {'line': b'      |                                                      \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[5.305044] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Ktemplate argument 3 is invalid\n'}
[5.305132] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Ktemplate argument 4 is invalid\n'}
[5.305225] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Ktemplate argument 5 is invalid\n'}
[5.305314] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:57:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected \xe2\x80\x98\x1b[01m\x1b[K;\x1b[m\x1b[K\xe2\x80\x99 at end of member declaration\n'}
[5.305401] (slpmu_ros2) StderrLine: {'line': b'   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::\x1b[01;31m\x1b[KSharedPtr\x1b[m\x1b[K bumper_sub_;\n'}
[5.305504] (slpmu_ros2) StderrLine: {'line': b'      |                                                         \x1b[01;31m\x1b[K^~~~~~~~~\x1b[m\x1b[K\n'}
[5.305594] (slpmu_ros2) StderrLine: {'line': b'      |                                                                  \x1b[32m\x1b[K;\x1b[m\x1b[K\n'}
[5.306953] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:67:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kbumper_sub_\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[5.307145] (slpmu_ros2) StderrLine: {'line': b'   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr \x1b[01;31m\x1b[Kbumper_sub_\x1b[m\x1b[K;\n'}
[5.307258] (slpmu_ros2) StderrLine: {'line': b'      |                                                                   \x1b[01;31m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.318224] (-) TimerEvent: {}
[5.419285] (-) TimerEvent: {}
[5.460200] (slpmu_ros2) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:9\x1b[m\x1b[K:\n'}
[5.460389] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:26:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Ksl_vcu_all\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[5.460489] (slpmu_ros2) StderrLine: {'line': b'   79 |     rclcpp::Subscription<\x1b[01;31m\x1b[Ksl_vcu_all\x1b[m\x1b[K::msg::BumperState>::SharedPtr bumper_sub_;\n'}
[5.460579] (slpmu_ros2) StderrLine: {'line': b'      |                          \x1b[01;31m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[5.460666] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Ktemplate argument 1 is invalid\n'}
[5.460753] (slpmu_ros2) StderrLine: {'line': b'   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState\x1b[01;31m\x1b[K>\x1b[m\x1b[K::SharedPtr bumper_sub_;\n'}
[5.460836] (slpmu_ros2) StderrLine: {'line': b'      |                                                      \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[5.460919] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Ktemplate argument 3 is invalid\n'}
[5.461004] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Ktemplate argument 4 is invalid\n'}
[5.461088] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Ktemplate argument 5 is invalid\n'}
[5.461195] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:57:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected \xe2\x80\x98\x1b[01m\x1b[K;\x1b[m\x1b[K\xe2\x80\x99 at end of member declaration\n'}
[5.461292] (slpmu_ros2) StderrLine: {'line': b'   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::\x1b[01;31m\x1b[KSharedPtr\x1b[m\x1b[K bumper_sub_;\n'}
[5.461384] (slpmu_ros2) StderrLine: {'line': b'      |                                                         \x1b[01;31m\x1b[K^~~~~~~~~\x1b[m\x1b[K\n'}
[5.461471] (slpmu_ros2) StderrLine: {'line': b'      |                                                                  \x1b[32m\x1b[K;\x1b[m\x1b[K\n'}
[5.462635] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:67:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kbumper_sub_\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[5.462877] (slpmu_ros2) StderrLine: {'line': b'   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr \x1b[01;31m\x1b[Kbumper_sub_\x1b[m\x1b[K;\n'}
[5.463041] (slpmu_ros2) StderrLine: {'line': b'      |                                                                   \x1b[01;31m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.519377] (-) TimerEvent: {}
[5.619650] (-) TimerEvent: {}
[5.719929] (-) TimerEvent: {}
[5.820222] (-) TimerEvent: {}
[5.920660] (-) TimerEvent: {}
[6.023382] (-) TimerEvent: {}
[6.123669] (-) TimerEvent: {}
[6.156010] (slpmu_ros2) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:90: CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o] Error 1\n'}
[6.156305] (slpmu_ros2) StderrLine: {'line': b'gmake[2]: *** Waiting for unfinished jobs....\n'}
[6.223788] (-) TimerEvent: {}
[6.324055] (-) TimerEvent: {}
[6.424353] (-) TimerEvent: {}
[6.524639] (-) TimerEvent: {}
[6.624927] (-) TimerEvent: {}
[6.725251] (-) TimerEvent: {}
[6.825533] (-) TimerEvent: {}
[6.925826] (-) TimerEvent: {}
[7.026116] (-) TimerEvent: {}
[7.126383] (-) TimerEvent: {}
[7.227260] (-) TimerEvent: {}
[7.327569] (-) TimerEvent: {}
[7.427868] (-) TimerEvent: {}
[7.528152] (-) TimerEvent: {}
[7.628413] (-) TimerEvent: {}
[7.728697] (-) TimerEvent: {}
[7.829141] (-) TimerEvent: {}
[7.929403] (-) TimerEvent: {}
[8.029687] (-) TimerEvent: {}
[8.129986] (-) TimerEvent: {}
[8.230281] (-) TimerEvent: {}
[8.330659] (-) TimerEvent: {}
[8.431101] (-) TimerEvent: {}
[8.531369] (-) TimerEvent: {}
[8.631669] (-) TimerEvent: {}
[8.731915] (-) TimerEvent: {}
[8.832227] (-) TimerEvent: {}
[8.932512] (-) TimerEvent: {}
[9.032802] (-) TimerEvent: {}
[9.133096] (-) TimerEvent: {}
[9.233382] (-) TimerEvent: {}
[9.333669] (-) TimerEvent: {}
[9.419475] (slpmu_ros2) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:76: CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o] Error 1\n'}
[9.419918] (slpmu_ros2) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:137: CMakeFiles/slpmu_node.dir/all] Error 2\n'}
[9.420091] (slpmu_ros2) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[9.422377] (slpmu_ros2) CommandEnded: {'returncode': 2}
[9.433745] (-) TimerEvent: {}
[9.434842] (slpmu_ros2) JobEnded: {'identifier': 'slpmu_ros2', 'rc': 2}
[9.445481] (-) EventReactorShutdown: {}
