[ 50%] Built target slpmu_motor
[100%] Built target motor_utils
-- Install configuration: "Release"
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/libslpmu_motor.a
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/i_motor_controller.hpp
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/motor_controller_can.hpp
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/slpmu_motor.hpp
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets.cmake
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets-release.cmake
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/slpmu_motor-config.cmake
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin/motor_utils
