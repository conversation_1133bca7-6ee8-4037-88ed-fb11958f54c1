[0.135s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.136s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x74bcfc7a9bb0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x74bcfc7a98b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x74bcfc7a98b0>>, mixin_verb=('build',))
[0.181s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.181s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.181s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.181s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.181s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.181s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.181s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/works/source/amr_vcu_test_ws'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ros'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['cmake', 'python']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'cmake'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['python_setup_py']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python_setup_py'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'cmake'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ros'
[0.220s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slcan' with type 'ros.cmake' and name 'slcan'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ros'
[0.221s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_jack' with type 'ros.cmake' and name 'slpmu_jack'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_meta'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ros']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ros'
[0.223s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_motor' with type 'ros.cmake' and name 'slpmu_motor'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ros'
[0.224s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_power' with type 'ros.cmake' and name 'slpmu_power'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore_ament_install'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_pkg']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_pkg'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_meta']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_meta'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ros']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ros'
[0.225s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_process' with type 'ros.cmake' and name 'slpmu_process'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_pkg']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_pkg'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_meta']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_meta'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ros']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ros'
[0.227s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_ros2' with type 'ros.ament_cmake' and name 'slpmu_ros2'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_pkg']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_pkg'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_meta']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ros'
[0.229s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sl_vcu_all' with type 'ros.ament_cmake' and name 'sl_vcu_all'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ignore'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ignore_ament_install'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['colcon_pkg']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['colcon_meta']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'colcon_meta'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['ros']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ros'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['cmake', 'python']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'cmake'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'python'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['python_setup_py']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'python_setup_py'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['ignore', 'ignore_ament_install']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ros'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['cmake', 'python']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'cmake'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'python'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['python_setup_py']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'python_setup_py'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'colcon_pkg'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['colcon_meta']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'colcon_meta'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['ros']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ros'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['cmake', 'python']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'cmake'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'python'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['python_setup_py']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'python_setup_py'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['colcon_meta']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'colcon_meta'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ros'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['cmake', 'python']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'cmake'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'python'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['python_setup_py']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'python_setup_py'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ros'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['cmake', 'python']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'cmake'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'python'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['python_setup_py']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'python_setup_py'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['ignore', 'ignore_ament_install']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['colcon_pkg']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'colcon_pkg'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['colcon_meta']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'colcon_meta'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['ros']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ros'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['cmake', 'python']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'cmake'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'python'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['python_setup_py']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'python_setup_py'
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.235s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.258s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.259s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.262s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 393 installed packages in /opt/ros/jazzy
[0.264s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.330s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_cache' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_first' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_force_configure' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'ament_cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.330s] DEBUG:colcon.colcon_core.verb:Building package 'sl_vcu_all' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all', 'symlink_install': False, 'test_result_base': None}
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_cache' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_first' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_force_configure' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'ament_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.331s] DEBUG:colcon.colcon_core.verb:Building package 'slcan' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan', 'symlink_install': False, 'test_result_base': None}
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_cache' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_first' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_force_configure' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'ament_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.331s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_process' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process', 'symlink_install': False, 'test_result_base': None}
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_args' from command line to 'None'
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target' from command line to 'None'
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_cache' from command line to 'False'
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_first' from command line to 'False'
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_force_configure' from command line to 'False'
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'ament_cmake_args' from command line to 'None'
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_cmake_args' from command line to 'None'
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.332s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_jack' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack', 'symlink_install': False, 'test_result_base': None}
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_args' from command line to 'None'
[0.332s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target' from command line to 'None'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_cache' from command line to 'False'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_first' from command line to 'False'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_force_configure' from command line to 'False'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'ament_cmake_args' from command line to 'None'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_cmake_args' from command line to 'None'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.333s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_motor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor', 'symlink_install': False, 'test_result_base': None}
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_args' from command line to 'None'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target' from command line to 'None'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_cache' from command line to 'False'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_first' from command line to 'False'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_force_configure' from command line to 'False'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'ament_cmake_args' from command line to 'None'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_cmake_args' from command line to 'None'
[0.333s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.333s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_power' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power', 'symlink_install': False, 'test_result_base': None}
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_args' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_cache' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_first' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_force_configure' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'ament_cmake_args' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_cmake_args' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.334s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_ros2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2', 'symlink_install': False, 'test_result_base': None}
[0.334s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.335s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.335s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan' with build type 'cmake'
[0.336s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan'
[0.338s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.338s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.338s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.342s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process' with build type 'cmake'
[0.343s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process'
[0.343s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.343s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.347s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all' with build type 'ament_cmake'
[0.347s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all'
[0.347s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.347s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.356s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.359s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process -- -j4 -l4
[0.364s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[0.442s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.463s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.480s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.480s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path')
[0.481s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.ps1'
[0.484s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.dsv'
[0.484s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.sh'
[0.488s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path_multiarch')
[0.489s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.ps1'
[0.490s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.dsv'
[0.491s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.sh'
[0.494s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slcan)
[0.504s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan' for CMake module files
[0.505s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan' for CMake config files
[0.505s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'cmake_prefix_path')
[0.506s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.ps1'
[0.506s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.dsv'
[0.506s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.sh'
[0.509s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib'
[0.510s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/bin'
[0.510s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig/slcan.pc'
[0.510s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/python3.12/site-packages'
[0.510s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/bin'
[0.511s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.ps1'
[0.513s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.dsv'
[0.514s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.sh'
[0.516s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.bash'
[0.517s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.zsh'
[0.518s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/colcon-core/packages/slcan)
[0.519s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor' with build type 'cmake'
[0.519s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor'
[0.519s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.519s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.529s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack' with build type 'cmake'
[0.529s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack'
[0.531s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.531s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.556s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor -- -j4 -l4
[0.571s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[0.744s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor -- -j4 -l4
[0.746s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor
[0.777s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path')
[0.777s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.ps1'
[0.778s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.dsv'
[0.778s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor
[0.780s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.sh'
[0.781s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path_multiarch')
[0.781s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.ps1'
[0.781s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.dsv'
[0.782s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.sh'
[0.784s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_motor)
[0.785s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor' for CMake module files
[0.785s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor' for CMake config files
[0.786s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'cmake_prefix_path')
[0.786s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.ps1'
[0.788s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.dsv'
[0.788s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.sh'
[0.791s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib'
[0.791s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin'
[0.791s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'path')
[0.791s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.ps1'
[0.792s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.dsv'
[0.795s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.sh'
[0.796s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig/slpmu_motor.pc'
[0.797s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/python3.12/site-packages'
[0.798s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin'
[0.798s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pythonscriptspath')
[0.798s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.ps1'
[0.799s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.dsv'
[0.801s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.sh'
[0.804s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.ps1'
[0.805s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.dsv'
[0.806s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.sh'
[0.807s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.bash'
[0.807s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.zsh'
[0.807s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/colcon-core/packages/slpmu_motor)
[0.810s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power' with build type 'cmake'
[0.810s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power'
[0.811s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.811s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.821s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[0.822s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[0.837s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power -- -j4 -l4
[0.861s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path')
[0.864s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.ps1'
[0.866s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.dsv'
[0.867s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.sh'
[0.868s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[0.869s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path_multiarch')
[0.869s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.ps1'
[0.870s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.dsv'
[0.870s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.sh'
[0.871s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_jack)
[0.872s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake module files
[0.872s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake config files
[0.873s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'cmake_prefix_path')
[0.873s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.ps1'
[0.874s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.dsv'
[0.874s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.sh'
[0.875s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib'
[0.876s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[0.876s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'path')
[0.876s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.ps1'
[0.879s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.dsv'
[0.880s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.sh'
[0.880s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig/slpmu_jack.pc'
[0.881s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/python3.12/site-packages'
[0.882s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[0.883s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pythonscriptspath')
[0.883s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.ps1'
[0.884s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.dsv'
[0.886s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.sh'
[0.888s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.ps1'
[0.890s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.dsv'
[0.891s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.sh'
[0.893s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.bash'
[0.897s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.zsh'
[0.898s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/colcon-core/packages/slpmu_jack)
[1.126s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power -- -j4 -l4
[1.126s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power
[1.142s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power
[1.143s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path')
[1.143s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.ps1'
[1.144s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.dsv'
[1.144s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.sh'
[1.145s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path_multiarch')
[1.145s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.ps1'
[1.146s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.dsv'
[1.147s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.sh'
[1.147s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_power)
[1.148s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power' for CMake module files
[1.148s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power' for CMake config files
[1.149s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'cmake_prefix_path')
[1.149s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.ps1'
[1.150s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.dsv'
[1.150s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.sh'
[1.151s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib'
[1.151s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin'
[1.151s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'path')
[1.152s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.ps1'
[1.152s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.dsv'
[1.153s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.sh'
[1.154s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/pkgconfig/slpmu_power.pc'
[1.154s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/python3.12/site-packages'
[1.154s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin'
[1.155s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pythonscriptspath')
[1.155s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.ps1'
[1.155s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.dsv'
[1.156s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.sh'
[1.157s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.ps1'
[1.158s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.dsv'
[1.159s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.sh'
[1.159s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.bash'
[1.160s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.zsh'
[1.160s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/colcon-core/packages/slpmu_power)
[1.248s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[1.249s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[1.370s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sl_vcu_all)
[1.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake module files
[1.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake config files
[1.373s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[1.373s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'cmake_prefix_path')
[1.374s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.ps1'
[1.374s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.dsv'
[1.375s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.sh'
[1.375s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib'
[1.375s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'ld_library_path_lib')
[1.376s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.ps1'
[1.376s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.dsv'
[1.376s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.sh'
[1.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/pkgconfig/sl_vcu_all.pc'
[1.378s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages'
[1.378s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'pythonpath')
[1.378s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.ps1'
[1.378s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.dsv'
[1.379s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.sh'
[1.380s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.380s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.ps1'
[1.380s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv'
[1.381s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.sh'
[1.382s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.bash'
[1.382s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.zsh'
[1.382s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/colcon-core/packages/sl_vcu_all)
[1.383s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sl_vcu_all)
[1.383s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake module files
[1.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake config files
[1.385s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'cmake_prefix_path')
[1.385s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.ps1'
[1.386s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.dsv'
[1.386s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.sh'
[1.387s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib'
[1.387s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'ld_library_path_lib')
[1.387s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.ps1'
[1.388s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.dsv'
[1.388s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.sh'
[1.389s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.389s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/pkgconfig/sl_vcu_all.pc'
[1.389s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages'
[1.389s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'pythonpath')
[1.389s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.ps1'
[1.390s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.dsv'
[1.390s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.sh'
[1.391s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.391s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.ps1'
[1.392s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv'
[1.392s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.sh'
[1.393s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.bash'
[1.393s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.zsh'
[1.395s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/colcon-core/packages/sl_vcu_all)
[1.439s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process -- -j4 -l4
[1.441s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process
[1.460s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path')
[1.461s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.ps1'
[1.461s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process
[1.462s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.dsv'
[1.463s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.sh'
[1.463s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path_multiarch')
[1.463s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.ps1'
[1.464s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.dsv'
[1.464s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.sh'
[1.465s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_process)
[1.466s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process' for CMake module files
[1.466s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process' for CMake config files
[1.467s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'cmake_prefix_path')
[1.467s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.ps1'
[1.467s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.dsv'
[1.468s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.sh'
[1.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib'
[1.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/bin'
[1.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig/slpmu_process.pc'
[1.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/python3.12/site-packages'
[1.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/bin'
[1.470s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.ps1'
[1.471s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.dsv'
[1.472s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.sh'
[1.472s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.bash'
[1.472s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.zsh'
[1.473s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/colcon-core/packages/slpmu_process)
[1.474s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2' with build type 'ament_cmake'
[1.474s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2'
[1.474s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.474s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.488s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[9.758s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[9.758s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_ros2)
[9.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake module files
[9.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake config files
[9.760s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'cmake_prefix_path')
[9.760s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.ps1'
[9.761s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.dsv'
[9.761s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.sh'
[9.762s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib'
[9.762s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'ld_library_path_lib')
[9.762s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.ps1'
[9.763s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.dsv'
[9.763s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.sh'
[9.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[9.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/pkgconfig/slpmu_ros2.pc'
[9.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages'
[9.764s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'pythonpath')
[9.765s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.ps1'
[9.765s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.dsv'
[9.765s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.sh'
[9.766s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[9.766s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.ps1'
[9.767s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv'
[9.768s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.sh'
[9.768s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.bash'
[9.769s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.zsh'
[9.769s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/colcon-core/packages/slpmu_ros2)
[9.780s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[9.780s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[9.780s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[9.780s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[9.799s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[9.799s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[9.799s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[9.812s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[9.812s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.ps1'
[9.813s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_ps1.py'
[9.815s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.ps1'
[9.816s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.sh'
[9.817s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_sh.py'
[9.817s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.sh'
[9.818s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.bash'
[9.819s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.bash'
[9.820s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.zsh'
[9.820s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.zsh'
