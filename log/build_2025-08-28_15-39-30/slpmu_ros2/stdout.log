-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
-- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
-- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
-- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
-- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
-- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Configured 'flake8' exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done (0.9s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[ 25%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
