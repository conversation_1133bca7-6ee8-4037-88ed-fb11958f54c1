[0.012s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[0.047s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.179s] -- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
[0.217s] -- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
[0.229s] -- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
[0.245s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.263s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.347s] -- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
[0.349s] -- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
[0.436s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.525s] -- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
[0.543s] -- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
[0.563s] -- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
[0.600s] -- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
[0.605s] -- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
[0.750s] -- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
[0.755s] -- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
[0.756s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.759s] -- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
[0.794s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[0.914s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.915s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include>
[0.915s] -- Configured cppcheck exclude dirs and/or files: 
[0.915s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.916s] -- Configured 'flake8' exclude dirs and/or files: 
[0.916s] -- Added test 'lint_cmake' to check CMake code style
[0.916s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.918s] -- Added test 'uncrustify' to check C / C++ code style
[0.918s] -- Configured uncrustify additional arguments: 
[0.919s] -- Added test 'xmllint' to check XML markup files
[0.920s] -- Configuring done (0.9s)
[0.941s] -- Generating done (0.0s)
[0.947s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[1.017s] [ 25%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[1.017s] [ 75%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o[0m
[1.017s] [ 75%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
[4.163s] In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_ros2_main.cpp:1[m[K:
[4.163s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:26:[m[K [01;31m[Kerror: [m[K‘[01m[Ksl_vcu_all[m[K’ was not declared in this scope
[4.163s]    79 |     rclcpp::Subscription<[01;31m[Ksl_vcu_all[m[K::msg::BumperState>::SharedPtr bumper_sub_;
[4.163s]       |                          [01;31m[K^~~~~~~~~~[m[K
[4.163s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
[4.163s]    79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState[01;31m[K>[m[K::SharedPtr bumper_sub_;
[4.164s]       |                                                      [01;31m[K^[m[K
[4.164s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 3 is invalid
[4.164s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 4 is invalid
[4.164s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 5 is invalid
[4.164s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:57:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
[4.164s]    79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::[01;31m[KSharedPtr[m[K bumper_sub_;
[4.164s]       |                                                         [01;31m[K^~~~~~~~~[m[K
[4.164s]       |                                                                  [32m[K;[m[K
[4.166s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:67:[m[K [01;31m[Kerror: [m[K‘[01m[Kbumper_sub_[m[K’ does not name a type
[4.166s]    79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr [01;31m[Kbumper_sub_[m[K;
[4.166s]       |                                                                   [01;31m[K^~~~~~~~~~~[m[K
[4.319s] In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:9[m[K:
[4.319s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:26:[m[K [01;31m[Kerror: [m[K‘[01m[Ksl_vcu_all[m[K’ was not declared in this scope
[4.319s]    79 |     rclcpp::Subscription<[01;31m[Ksl_vcu_all[m[K::msg::BumperState>::SharedPtr bumper_sub_;
[4.319s]       |                          [01;31m[K^~~~~~~~~~[m[K
[4.319s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
[4.319s]    79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState[01;31m[K>[m[K::SharedPtr bumper_sub_;
[4.319s]       |                                                      [01;31m[K^[m[K
[4.319s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 3 is invalid
[4.320s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 4 is invalid
[4.320s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 5 is invalid
[4.320s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:57:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
[4.320s]    79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::[01;31m[KSharedPtr[m[K bumper_sub_;
[4.320s]       |                                                         [01;31m[K^~~~~~~~~[m[K
[4.320s]       |                                                                  [32m[K;[m[K
[4.321s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:67:[m[K [01;31m[Kerror: [m[K‘[01m[Kbumper_sub_[m[K’ does not name a type
[4.321s]    79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr [01;31m[Kbumper_sub_[m[K;
[4.322s]       |                                                                   [01;31m[K^~~~~~~~~~~[m[K
[5.015s] gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:90: CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o] Error 1
[5.015s] gmake[2]: *** Waiting for unfinished jobs....
[8.278s] gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:76: CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o] Error 1
[8.279s] gmake[1]: *** [CMakeFiles/Makefile2:137: CMakeFiles/slpmu_node.dir/all] Error 2
[8.279s] gmake: *** [Makefile:146: all] Error 2
[8.281s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
