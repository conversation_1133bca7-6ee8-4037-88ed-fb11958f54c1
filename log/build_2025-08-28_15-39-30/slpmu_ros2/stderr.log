In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_ros2_main.cpp:1[m[K:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:26:[m[K [01;31m[Kerror: [m[K‘[01m[Ksl_vcu_all[m[K’ was not declared in this scope
   79 |     rclcpp::Subscription<[01;31m[Ksl_vcu_all[m[K::msg::BumperState>::SharedPtr bumper_sub_;
      |                          [01;31m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState[01;31m[K>[m[K::SharedPtr bumper_sub_;
      |                                                      [01;31m[K^[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 3 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 4 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 5 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:57:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::[01;31m[KSharedPtr[m[K bumper_sub_;
      |                                                         [01;31m[K^~~~~~~~~[m[K
      |                                                                  [32m[K;[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:67:[m[K [01;31m[Kerror: [m[K‘[01m[Kbumper_sub_[m[K’ does not name a type
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr [01;31m[Kbumper_sub_[m[K;
      |                                                                   [01;31m[K^~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:9[m[K:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:26:[m[K [01;31m[Kerror: [m[K‘[01m[Ksl_vcu_all[m[K’ was not declared in this scope
   79 |     rclcpp::Subscription<[01;31m[Ksl_vcu_all[m[K::msg::BumperState>::SharedPtr bumper_sub_;
      |                          [01;31m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState[01;31m[K>[m[K::SharedPtr bumper_sub_;
      |                                                      [01;31m[K^[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 3 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 4 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 5 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:57:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::[01;31m[KSharedPtr[m[K bumper_sub_;
      |                                                         [01;31m[K^~~~~~~~~[m[K
      |                                                                  [32m[K;[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:67:[m[K [01;31m[Kerror: [m[K‘[01m[Kbumper_sub_[m[K’ does not name a type
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr [01;31m[Kbumper_sub_[m[K;
      |                                                                   [01;31m[K^~~~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:90: CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o] Error 1
gmake[2]: *** Waiting for unfinished jobs....
gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:76: CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:137: CMakeFiles/slpmu_node.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
