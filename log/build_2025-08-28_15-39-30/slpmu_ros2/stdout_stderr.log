-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
-- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
-- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
-- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
-- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
-- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Configured 'flake8' exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done (0.9s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[ 25%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_ros2_main.cpp:1[m[K:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:26:[m[K [01;31m[Kerror: [m[K‘[01m[Ksl_vcu_all[m[K’ was not declared in this scope
   79 |     rclcpp::Subscription<[01;31m[Ksl_vcu_all[m[K::msg::BumperState>::SharedPtr bumper_sub_;
      |                          [01;31m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState[01;31m[K>[m[K::SharedPtr bumper_sub_;
      |                                                      [01;31m[K^[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 3 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 4 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 5 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:57:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::[01;31m[KSharedPtr[m[K bumper_sub_;
      |                                                         [01;31m[K^~~~~~~~~[m[K
      |                                                                  [32m[K;[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:67:[m[K [01;31m[Kerror: [m[K‘[01m[Kbumper_sub_[m[K’ does not name a type
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr [01;31m[Kbumper_sub_[m[K;
      |                                                                   [01;31m[K^~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:9[m[K:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:26:[m[K [01;31m[Kerror: [m[K‘[01m[Ksl_vcu_all[m[K’ was not declared in this scope
   79 |     rclcpp::Subscription<[01;31m[Ksl_vcu_all[m[K::msg::BumperState>::SharedPtr bumper_sub_;
      |                          [01;31m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState[01;31m[K>[m[K::SharedPtr bumper_sub_;
      |                                                      [01;31m[K^[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 3 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 4 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:54:[m[K [01;31m[Kerror: [m[Ktemplate argument 5 is invalid
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:57:[m[K [01;31m[Kerror: [m[Kexpected ‘[01m[K;[m[K’ at end of member declaration
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::[01;31m[KSharedPtr[m[K bumper_sub_;
      |                                                         [01;31m[K^~~~~~~~~[m[K
      |                                                                  [32m[K;[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:79:67:[m[K [01;31m[Kerror: [m[K‘[01m[Kbumper_sub_[m[K’ does not name a type
   79 |     rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr [01;31m[Kbumper_sub_[m[K;
      |                                                                   [01;31m[K^~~~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:90: CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o] Error 1
gmake[2]: *** Waiting for unfinished jobs....
gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:76: CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:137: CMakeFiles/slpmu_node.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
