[0.017s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[0.131s] [ 50%] Built target slpmu_jack
[0.190s] [100%] Built target jack_utils
[0.212s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[0.213s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[0.246s] -- Install configuration: "Release"
[0.246s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin/jack_utils
[0.246s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/libslpmu_jack.a
[0.247s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include
[0.247s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu
[0.248s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack
[0.248s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/i_jack_controller.hpp
[0.248s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp
[0.248s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/slpmu_jack.hpp
[0.248s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets.cmake
[0.248s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets-release.cmake
[0.251s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/slpmu_jack-config.cmake
[0.252s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
