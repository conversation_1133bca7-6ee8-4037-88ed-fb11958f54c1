[0.000000] (-) TimerEvent: {}
[0.000629] (sl_vcu_all) JobQueued: {'identifier': 'sl_vcu_all', 'dependencies': OrderedDict()}
[0.000811] (slcan) JobQueued: {'identifier': 'slcan', 'dependencies': OrderedDict()}
[0.000980] (slpmu_process) JobQueued: {'identifier': 'slpmu_process', 'dependencies': OrderedDict()}
[0.001030] (slpmu_jack) JobQueued: {'identifier': 'slpmu_jack', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001066] (slpmu_motor) JobQueued: {'identifier': 'slpmu_motor', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001089] (slpmu_power) JobQueued: {'identifier': 'slpmu_power', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001120] (slpmu_ros2) JobQueued: {'identifier': 'slpmu_ros2', 'dependencies': OrderedDict({'sl_vcu_all': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all', 'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan', 'slpmu_process': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process', 'slpmu_motor': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor'})}
[0.001152] (slcan) JobStarted: {'identifier': 'slcan'}
[0.008094] (sl_vcu_all) JobStarted: {'identifier': 'sl_vcu_all'}
[0.012720] (slpmu_process) JobStarted: {'identifier': 'slpmu_process'}
[0.018818] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'cmake'}
[0.019274] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'build'}
[0.019942] (slcan) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.022679] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'cmake'}
[0.023524] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'build'}
[0.024135] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.028466] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'cmake'}
[0.029120] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'build'}
[0.029729] (slpmu_process) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.097186] (slcan) StdoutLine: {'line': b'[100%] Built target slcan\n'}
[0.099673] (-) TimerEvent: {}
[0.102765] (sl_vcu_all) StdoutLine: {'line': b'[  0%] Built target sl_vcu_all__rosidl_generator_type_description\n'}
[0.103008] (slpmu_process) StdoutLine: {'line': b'[100%] Built target slpmu_process\n'}
[0.119143] (slpmu_process) CommandEnded: {'returncode': 0}
[0.119752] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'install'}
[0.125162] (sl_vcu_all) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_sl_vcu_all\n'}
[0.132189] (slpmu_process) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.135215] (slcan) CommandEnded: {'returncode': 0}
[0.135566] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'install'}
[0.135596] (slcan) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.138706] (sl_vcu_all) StdoutLine: {'line': b'[  1%] Built target sl_vcu_all__cpp\n'}
[0.145471] (slpmu_process) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.145701] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/libslpmu_process.a\n'}
[0.146049] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include\n'}
[0.146456] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu\n'}
[0.146824] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/process\n'}
[0.147162] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/process/imu_odom_fusion.hpp\n'}
[0.147320] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/slpmu_process.hpp\n'}
[0.147813] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/cmake/slpmu_process_targets.cmake\n'}
[0.147974] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/cmake/slpmu_process_targets-release.cmake\n'}
[0.148346] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/slpmu_process-config.cmake\n'}
[0.149456] (slcan) StdoutLine: {'line': b'-- Install configuration: "RelWithDebInfo"\n'}
[0.149605] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a\n'}
[0.149707] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include\n'}
[0.150059] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan\n'}
[0.150196] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan.hpp\n'}
[0.150293] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan\n'}
[0.150804] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/canopen.hpp\n'}
[0.150921] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/socket_can.hpp\n'}
[0.151174] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets.cmake\n'}
[0.151653] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets-relwithdebinfo.cmake\n'}
[0.151779] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/slcan-config.cmake\n'}
[0.153635] (slpmu_process) CommandEnded: {'returncode': 0}
[0.155586] (sl_vcu_all) StdoutLine: {'line': b'[ 25%] Built target sl_vcu_all__rosidl_generator_c\n'}
[0.177793] (slpmu_process) JobEnded: {'identifier': 'slpmu_process', 'rc': 0}
[0.178223] (slcan) CommandEnded: {'returncode': 0}
[0.192159] (slcan) JobEnded: {'identifier': 'slcan', 'rc': 0}
[0.192551] (slpmu_motor) JobStarted: {'identifier': 'slpmu_motor'}
[0.200003] (slpmu_jack) JobStarted: {'identifier': 'slpmu_jack'}
[0.200099] (-) TimerEvent: {}
[0.203628] (slpmu_power) JobStarted: {'identifier': 'slpmu_power'}
[0.212128] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'cmake'}
[0.212226] (sl_vcu_all) StdoutLine: {'line': b'[ 34%] Built target sl_vcu_all__rosidl_typesupport_cpp\n'}
[0.212900] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'build'}
[0.213349] (slpmu_motor) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.215876] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'cmake'}
[0.216452] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'build'}
[0.216684] (slpmu_jack) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.218206] (sl_vcu_all) StdoutLine: {'line': b'[ 42%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_c\n'}
[0.224749] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'cmake'}
[0.224798] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'build'}
[0.224820] (slpmu_power) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.227211] (sl_vcu_all) StdoutLine: {'line': b'[ 51%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_cpp\n'}
[0.278153] (sl_vcu_all) StdoutLine: {'line': b'[ 59%] Built target sl_vcu_all__rosidl_typesupport_introspection_c\n'}
[0.293549] (sl_vcu_all) StdoutLine: {'line': b'[ 68%] Built target sl_vcu_all__rosidl_typesupport_introspection_cpp\n'}
[0.300152] (-) TimerEvent: {}
[0.330499] (slpmu_jack) StdoutLine: {'line': b'[ 50%] Built target slpmu_jack\n'}
[0.345351] (sl_vcu_all) StdoutLine: {'line': b'[ 77%] Built target sl_vcu_all__rosidl_typesupport_c\n'}
[0.350046] (sl_vcu_all) StdoutLine: {'line': b'[ 78%] Built target can_frame_dispatcher_node\n'}
[0.350553] (slpmu_motor) StdoutLine: {'line': b'[ 50%] Built target slpmu_motor\n'}
[0.360241] (sl_vcu_all) StdoutLine: {'line': b'[ 79%] Built target zl_motor_controller_node\n'}
[0.383105] (sl_vcu_all) StdoutLine: {'line': b'[ 81%] Built target zl_motor_modbus_controller_node\n'}
[0.389184] (slpmu_jack) StdoutLine: {'line': b'[100%] Built target jack_utils\n'}
[0.400235] (-) TimerEvent: {}
[0.411481] (slpmu_jack) CommandEnded: {'returncode': 0}
[0.412124] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'install'}
[0.412220] (slpmu_jack) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.415236] (slpmu_motor) StdoutLine: {'line': b'[100%] Built target motor_utils\n'}
[0.416459] (sl_vcu_all) StdoutLine: {'line': b'[ 82%] Built target bumper_sensor_node\n'}
[0.424284] (sl_vcu_all) StdoutLine: {'line': b'[ 83%] Built target imu_sensor_node\n'}
[0.434772] (slpmu_power) StdoutLine: {'line': b'[ 50%] Built target slpmu_power\n'}
[0.443932] (slpmu_motor) CommandEnded: {'returncode': 0}
[0.444578] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'install'}
[0.444683] (slpmu_motor) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.445278] (slpmu_jack) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.445673] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin/jack_utils\n'}
[0.446053] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/libslpmu_jack.a\n'}
[0.446449] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include\n'}
[0.446822] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu\n'}
[0.447565] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack\n'}
[0.447693] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/i_jack_controller.hpp\n'}
[0.447784] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp\n'}
[0.447871] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/slpmu_jack.hpp\n'}
[0.447955] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets.cmake\n'}
[0.448038] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets-release.cmake\n'}
[0.449270] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/slpmu_jack-config.cmake\n'}
[0.451599] (slpmu_jack) CommandEnded: {'returncode': 0}
[0.455456] (slpmu_motor) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.455713] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/libslpmu_motor.a\n'}
[0.461223] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include\n'}
[0.461383] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu\n'}
[0.461486] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor\n'}
[0.461576] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/i_motor_controller.hpp\n'}
[0.461677] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/motor_controller_can.hpp\n'}
[0.461766] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/slpmu_motor.hpp\n'}
[0.461858] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets.cmake\n'}
[0.461947] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets-release.cmake\n'}
[0.462036] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/slpmu_motor-config.cmake\n'}
[0.462134] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin/motor_utils\n'}
[0.462275] (sl_vcu_all) StdoutLine: {'line': b'[ 84%] Built target teleop_key\n'}
[0.473098] (slpmu_jack) JobEnded: {'identifier': 'slpmu_jack', 'rc': 0}
[0.474131] (slpmu_motor) CommandEnded: {'returncode': 0}
[0.482416] (slpmu_power) StdoutLine: {'line': b'[100%] Built target power_utils\n'}
[0.490179] (sl_vcu_all) StdoutLine: {'line': b'[ 85%] Built target battery_monitor_node\n'}
[0.496292] (sl_vcu_all) StdoutLine: {'line': b'[ 87%] Built target jack_control_node\n'}
[0.500365] (-) TimerEvent: {}
[0.500766] (slpmu_motor) JobEnded: {'identifier': 'slpmu_motor', 'rc': 0}
[0.502641] (slpmu_power) CommandEnded: {'returncode': 0}
[0.503306] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'install'}
[0.503402] (slpmu_power) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.513257] (slpmu_power) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.514270] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/libslpmu_power.a\n'}
[0.514773] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include\n'}
[0.515182] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu\n'}
[0.515589] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/slpmu_power.hpp\n'}
[0.515994] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power\n'}
[0.517246] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/i_battery.hpp\n'}
[0.517696] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/battery_can.hpp\n'}
[0.518105] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets.cmake\n'}
[0.518829] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets-noconfig.cmake\n'}
[0.519873] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/slpmu_power-config.cmake\n'}
[0.520318] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin/power_utils\n'}
[0.520722] (slpmu_power) CommandEnded: {'returncode': 0}
[0.522001] (sl_vcu_all) StdoutLine: {'line': b'[ 87%] Built target sl_vcu_all\n'}
[0.524870] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target led_display_control_node\n'}
[0.536380] (slpmu_power) JobEnded: {'identifier': 'slpmu_power', 'rc': 0}
[0.551232] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target sl_vcu_all__py\n'}
[0.562001] (sl_vcu_all) StdoutLine: {'line': b'running egg_info\n'}
[0.591436] (sl_vcu_all) StdoutLine: {'line': b'[ 96%] Built target sl_vcu_all__rosidl_generator_py\n'}
[0.596258] (sl_vcu_all) StdoutLine: {'line': b'writing sl_vcu_all.egg-info/PKG-INFO\n'}
[0.596445] (sl_vcu_all) StdoutLine: {'line': b'writing dependency_links to sl_vcu_all.egg-info/dependency_links.txt\n'}
[0.596548] (sl_vcu_all) StdoutLine: {'line': b'writing top-level names to sl_vcu_all.egg-info/top_level.txt\n'}
[0.606169] (-) TimerEvent: {}
[0.622959] (sl_vcu_all) StdoutLine: {'line': b'[ 98%] Built target sl_vcu_all_s__rosidl_typesupport_fastrtps_c\n'}
[0.624145] (sl_vcu_all) StdoutLine: {'line': b'[ 99%] Built target sl_vcu_all_s__rosidl_typesupport_c\n'}
[0.645542] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target sl_vcu_all_s__rosidl_typesupport_introspection_c\n'}
[0.692199] (sl_vcu_all) StdoutLine: {'line': b"reading manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[0.693133] (sl_vcu_all) StdoutLine: {'line': b"writing manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[0.706263] (-) TimerEvent: {}
[0.734335] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_sl_vcu_all_egg\n'}
[0.751803] (sl_vcu_all) CommandEnded: {'returncode': 0}
[0.752614] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'install'}
[0.753579] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.765060] (sl_vcu_all) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.765646] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/rosidl_interfaces/sl_vcu_all\n'}
[0.766181] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.json\n'}
[0.766332] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.json\n'}
[0.766836] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.json\n'}
[0.767024] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.json\n'}
[0.767194] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.json\n'}
[0.767721] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.json\n'}
[0.768207] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.json\n'}
[0.768317] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.json\n'}
[0.768461] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.json\n'}
[0.768686] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.json\n'}
[0.768789] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.json\n'}
[0.768912] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.json\n'}
[0.769060] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.769189] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.769341] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.769423] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.h\n'}
[0.769501] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.h\n'}
[0.769587] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.h\n'}
[0.769674] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.c\n'}
[0.769757] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[0.769856] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__description.c\n'}
[0.769935] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.h\n'}
[0.770013] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.770091] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.h\n'}
[0.770433] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.770601] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.h\n'}
[0.770685] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.h\n'}
[0.770777] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.c\n'}
[0.770858] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.h\n'}
[0.770936] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[0.771083] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[0.771300] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.h\n'}
[0.771513] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__description.c\n'}
[0.771784] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.h\n'}
[0.771871] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.c\n'}
[0.771950] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[0.772075] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.c\n'}
[0.772206] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__description.c\n'}
[0.772296] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__description.c\n'}
[0.772462] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.h\n'}
[0.772659] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.h\n'}
[0.772906] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.h\n'}
[0.773145] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.h\n'}
[0.773361] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.h\n'}
[0.773637] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.h\n'}
[0.773869] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.774099] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.h\n'}
[0.774378] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.h\n'}
[0.774539] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.h\n'}
[0.774703] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.h\n'}
[0.774963] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_c__visibility_control.h\n'}
[0.775129] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.h\n'}
[0.775376] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.775468] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.h\n'}
[0.775554] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__description.c\n'}
[0.775649] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[0.775766] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__description.c\n'}
[0.775849] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.c\n'}
[0.775962] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.c\n'}
[0.776164] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.h\n'}
[0.776263] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[0.776347] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.h\n'}
[0.776492] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[0.776642] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.h\n'}
[0.776758] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.h\n'}
[0.776850] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.h\n'}
[0.777006] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.c\n'}
[0.777098] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.h\n'}
[0.777299] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[0.777402] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[0.777531] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.h\n'}
[0.777662] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[0.777799] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__description.c\n'}
[0.777966] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__description.c\n'}
[0.778058] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.h\n'}
[0.778201] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.h\n'}
[0.778319] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.h\n'}
[0.778425] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.c\n'}
[0.778544] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.h\n'}
[0.778655] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.h\n'}
[0.778823] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.h\n'}
[0.778947] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.h\n'}
[0.779032] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[0.779171] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[0.779257] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__description.c\n'}
[0.779338] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.h\n'}
[0.779418] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.c\n'}
[0.779543] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.h\n'}
[0.779627] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.c\n'}
[0.779714] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__description.c\n'}
[0.779935] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.h\n'}
[0.780162] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__description.c\n'}
[0.780375] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.c\n'}
[0.780669] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__description.c\n'}
[0.780885] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.h\n'}
[0.780974] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.h\n'}
[0.781058] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.h\n'}
[0.781164] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.h\n'}
[0.781250] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.c\n'}
[0.781331] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.h\n'}
[0.781412] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.h\n'}
[0.781493] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.h\n'}
[0.781607] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.h\n'}
[0.781697] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.h\n'}
[0.781801] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.sh\n'}
[0.782017] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.dsv\n'}
[0.782116] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_c.so\n'}
[0.782257] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.782399] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.782536] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.782640] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_c.h\n'}
[0.782932] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.783257] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.783511] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_c.h\n'}
[0.783773] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_c.h\n'}
[0.783941] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.784028] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.784128] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.784252] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.784337] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_c.h\n'}
[0.784419] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.784500] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.784592] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.784677] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_c.h\n'}
[0.784761] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_c.h\n'}
[0.784852] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_c.h\n'}
[0.784935] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.785018] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_c.so\n'}
[0.785100] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.785227] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.785312] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.785395] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__builder.hpp\n'}
[0.785483] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__traits.hpp\n'}
[0.785563] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.hpp\n'}
[0.785645] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.hpp\n'}
[0.785735] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.hpp\n'}
[0.785816] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.785896] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.hpp\n'}
[0.785975] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.786075] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.hpp\n'}
[0.786172] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__builder.hpp\n'}
[0.786254] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__builder.hpp\n'}
[0.786335] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.hpp\n'}
[0.786417] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__builder.hpp\n'}
[0.786497] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.hpp\n'}
[0.786580] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__traits.hpp\n'}
[0.786668] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__traits.hpp\n'}
[0.786764] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.hpp\n'}
[0.786851] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__traits.hpp\n'}
[0.786941] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.hpp\n'}
[0.787028] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.hpp\n'}
[0.787125] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.hpp\n'}
[0.787216] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.hpp\n'}
[0.787298] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.787378] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.hpp\n'}
[0.787460] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.hpp\n'}
[0.787547] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.hpp\n'}
[0.787629] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.hpp\n'}
[0.787710] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.787796] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__traits.hpp\n'}
[0.787878] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.hpp\n'}
[0.787959] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__builder.hpp\n'}
[0.788040] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__traits.hpp\n'}
[0.788126] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__traits.hpp\n'}
[0.788208] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.hpp\n'}
[0.788288] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__builder.hpp\n'}
[0.788373] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__traits.hpp\n'}
[0.788459] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__builder.hpp\n'}
[0.788546] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.hpp\n'}
[0.788643] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__traits.hpp\n'}
[0.788733] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__traits.hpp\n'}
[0.788817] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__builder.hpp\n'}
[0.788903] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__builder.hpp\n'}
[0.788988] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__traits.hpp\n'}
[0.789075] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.hpp\n'}
[0.789165] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.hpp\n'}
[0.789247] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.hpp\n'}
[0.789334] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.hpp\n'}
[0.789420] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.hpp\n'}
[0.789509] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.hpp\n'}
[0.789595] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__builder.hpp\n'}
[0.789678] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.hpp\n'}
[0.789762] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__traits.hpp\n'}
[0.789847] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.hpp\n'}
[0.789933] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.hpp\n'}
[0.790020] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.hpp\n'}
[0.790103] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.hpp\n'}
[0.790282] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.hpp\n'}
[0.790367] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__builder.hpp\n'}
[0.790449] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__builder.hpp\n'}
[0.790537] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.hpp\n'}
[0.790624] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.hpp\n'}
[0.790707] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.hpp\n'}
[0.790788] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.hpp\n'}
[0.790869] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.790951] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.hpp\n'}
[0.791034] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.791174] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.791267] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.791352] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/dds_fastrtps\n'}
[0.791452] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.792253] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.792336] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.792423] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/dds_fastrtps\n'}
[0.792978] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.793069] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.793181] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.793974] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.794063] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.794156] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/dds_fastrtps\n'}
[0.794713] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.794802] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.794895] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.795688] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.795777] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.795868] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.796422] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.796518] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.796607] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.797399] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.797489] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.797576] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.797661] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.798226] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_c.h\n'}
[0.798314] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[0.799098] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.799200] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.799288] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_c.h\n'}
[0.799389] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[0.799952] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[0.800040] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_c.h\n'}
[0.800829] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[0.800931] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_c.h\n'}
[0.801023] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.801119] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.801681] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.801767] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[0.802557] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_c.h\n'}
[0.802656] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_c.h\n'}
[0.802745] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[0.802828] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[0.803393] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_c.h\n'}
[0.803481] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_c.h\n'}
[0.803568] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[0.803655] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[0.803742] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[0.803829] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[0.803910] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[0.803991] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_c.h\n'}
[0.804071] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_c.h\n'}
[0.804175] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_c.h\n'}
[0.804258] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_c.h\n'}
[0.804345] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_c.so\n'}
[0.804438] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_c.so\n'}
[0.804518] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.804606] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.804708] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.804791] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.804872] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.cpp\n'}
[0.804970] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.805053] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.805146] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.cpp\n'}
[0.805234] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.cpp\n'}
[0.805318] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.805406] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.cpp\n'}
[0.805491] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.805577] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.805663] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.805749] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.805836] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.cpp\n'}
[0.805925] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.806048] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.cpp\n'}
[0.806142] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.cpp\n'}
[0.806230] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.cpp\n'}
[0.806314] (-) TimerEvent: {}
[0.806559] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.806676] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.806766] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.806854] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.806938] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.807019] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.cpp\n'}
[0.807108] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.cpp\n'}
[0.807307] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.807397] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.cpp\n'}
[0.807483] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.807579] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.cpp\n'}
[0.807674] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_cpp.so\n'}
[0.807756] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_cpp.so\n'}
[0.807954] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.sh\n'}
[0.808052] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.dsv\n'}
[0.808146] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info\n'}
[0.808232] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/SOURCES.txt\n'}
[0.808322] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/PKG-INFO\n'}
[0.808413] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/top_level.txt\n'}
[0.808495] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/dependency_links.txt\n'}
[0.808576] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all\n'}
[0.808670] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.808758] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[0.808844] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_c.c\n'}
[0.808927] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action\n'}
[0.809010] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/__init__.py\n'}
[0.809097] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control.py\n'}
[0.809187] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control_s.c\n'}
[0.809274] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.809357] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/__init__.py\n'}
[0.809442] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv\n'}
[0.809530] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status_s.c\n'}
[0.809617] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/__init__.py\n'}
[0.809704] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control.py\n'}
[0.809794] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control_s.c\n'}
[0.809876] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status.py\n'}
[0.809957] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter.py\n'}
[0.810042] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter_s.c\n'}
[0.810144] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[0.810249] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg\n'}
[0.810335] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info.py\n'}
[0.810422] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame_s.c\n'}
[0.810511] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data_s.c\n'}
[0.810599] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info_s.c\n'}
[0.810680] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data.py\n'}
[0.810767] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status_s.c\n'}
[0.810853] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status.py\n'}
[0.810938] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state_s.c\n'}
[0.811021] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/__init__.py\n'}
[0.811116] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status_s.c\n'}
[0.811199] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state.py\n'}
[0.811290] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status.py\n'}
[0.811371] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame.py\n'}
[0.811458] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state.py\n'}
[0.811540] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data.py\n'}
[0.811624] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state_s.c\n'}
[0.811709] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data_s.c\n'}
[0.811794] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[0.827470] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all'...\n"}
[0.827651] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action'...\n"}
[0.827748] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg'...\n"}
[0.827839] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv'...\n"}
[0.834638] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[0.835099] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[0.835522] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[0.836052] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_py.so\n'}
[0.836306] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.idl\n'}
[0.836463] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.idl\n'}
[0.836693] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.idl\n'}
[0.836807] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.idl\n'}
[0.837203] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.idl\n'}
[0.837333] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.idl\n'}
[0.837545] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.idl\n'}
[0.837720] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.idl\n'}
[0.837978] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.idl\n'}
[0.838095] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.idl\n'}
[0.838195] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.idl\n'}
[0.838325] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.idl\n'}
[0.838411] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.srv\n'}
[0.838492] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.srv\n'}
[0.838582] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.srv\n'}
[0.838667] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.msg\n'}
[0.838750] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.msg\n'}
[0.838844] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.msg\n'}
[0.839001] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.msg\n'}
[0.839090] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.msg\n'}
[0.839182] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.msg\n'}
[0.839268] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.msg\n'}
[0.839355] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.msg\n'}
[0.839490] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.action\n'}
[0.839597] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/can_frame_dispatcher_node\n'}
[0.839681] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_controller_node\n'}
[0.839763] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_modbus_controller_node\n'}
[0.839970] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/bumper_sensor_node\n'}
[0.840499] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/imu_sensor_node\n'}
[0.840993] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/teleop_key\n'}
[0.841504] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/battery_monitor_node\n'}
[0.842001] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/jack_control_node\n'}
[0.842536] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/led_display_control_node\n'}
[0.842816] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch\n'}
[0.843137] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/battery_monitor.launch.py\n'}
[0.843402] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/jack_control.launch.py\n'}
[0.843568] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/robot_localization_ekf.launch.py\n'}
[0.843734] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_controller.launch.py\n'}
[0.843867] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_modbus_controller.launch.py\n'}
[0.843966] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/led_control.launch.py\n'}
[0.844245] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/vcu_nodes.launch.py\n'}
[0.844365] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/imu_sensor.launch.py\n'}
[0.844492] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/bumper_sensor.launch.py\n'}
[0.844590] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/sl_vcu_all.launch.py\n'}
[0.844721] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config\n'}
[0.844811] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/ekf.yaml\n'}
[0.844924] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_modbus_controller.yaml\n'}
[0.845016] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/battery_monitor.yaml\n'}
[0.845104] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_controller.yaml\n'}
[0.845356] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/jack_control.yaml\n'}
[0.845472] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/imu_sensor.yaml\n'}
[0.845568] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/robot_localization_ekf.yaml\n'}
[0.845660] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/bumper_sensor.yaml\n'}
[0.845750] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/led_control.yaml\n'}
[0.845834] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/package_run_dependencies/sl_vcu_all\n'}
[0.845928] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/parent_prefix_path/sl_vcu_all\n'}
[0.846020] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.sh\n'}
[0.846118] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.dsv\n'}
[0.847333] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.sh\n'}
[0.847435] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.dsv\n'}
[0.847525] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.bash\n'}
[0.847617] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.sh\n'}
[0.847706] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.zsh\n'}
[0.847792] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.dsv\n'}
[0.847883] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv\n'}
[0.847967] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/packages/sl_vcu_all\n'}
[0.848050] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport.cmake\n'}
[0.848152] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport-noconfig.cmake\n'}
[0.848244] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[0.848332] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[0.848417] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cppExport.cmake\n'}
[0.848504] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[0.848602] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[0.848703] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport.cmake\n'}
[0.850737] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[0.850895] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport.cmake\n'}
[0.850999] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport-noconfig.cmake\n'}
[0.851096] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport.cmake\n'}
[0.851194] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[0.851289] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport.cmake\n'}
[0.851380] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[0.851473] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport.cmake\n'}
[0.851566] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport-noconfig.cmake\n'}
[0.851656] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake-extras.cmake\n'}
[0.851745] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.851833] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.851924] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.853640] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.853808] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[0.853912] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[0.854003] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig.cmake\n'}
[0.854091] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig-version.cmake\n'}
[0.855749] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.xml\n'}
[0.859259] (sl_vcu_all) CommandEnded: {'returncode': 0}
[0.886958] (sl_vcu_all) JobEnded: {'identifier': 'sl_vcu_all', 'rc': 0}
[0.888811] (slpmu_ros2) JobStarted: {'identifier': 'slpmu_ros2'}
[0.905201] (slpmu_ros2) JobProgress: {'identifier': 'slpmu_ros2', 'progress': 'cmake'}
[0.905930] (slpmu_ros2) JobProgress: {'identifier': 'slpmu_ros2', 'progress': 'build'}
[0.906267] (slpmu_ros2) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.906945] (-) TimerEvent: {}
[0.994144] (slpmu_ros2) StdoutLine: {'line': b'[  2%] Built target slpmu_ros2__rosidl_generator_type_description\n'}
[1.006715] (slpmu_ros2) StdoutLine: {'line': b'[  2%] Built target ament_cmake_python_copy_slpmu_ros2\n'}
[1.009016] (-) TimerEvent: {}
[1.035952] (slpmu_ros2) StdoutLine: {'line': b'[  5%] Built target slpmu_ros2__cpp\n'}
[1.038437] (slpmu_ros2) StdoutLine: {'line': b'[ 18%] Built target slpmu_ros2__rosidl_generator_c\n'}
[1.079295] (slpmu_ros2) StdoutLine: {'line': b'[ 26%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c\n'}
[1.094063] (slpmu_ros2) StdoutLine: {'line': b'[ 34%] Built target slpmu_ros2__rosidl_typesupport_cpp\n'}
[1.109314] (-) TimerEvent: {}
[1.128502] (slpmu_ros2) StdoutLine: {'line': b'[ 42%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp\n'}
[1.128985] (slpmu_ros2) StdoutLine: {'line': b'[ 50%] Built target slpmu_ros2__rosidl_typesupport_c\n'}
[1.140343] (slpmu_ros2) StdoutLine: {'line': b'[ 57%] Built target slpmu_ros2__rosidl_typesupport_introspection_c\n'}
[1.166194] (slpmu_ros2) StdoutLine: {'line': b'[ 65%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp\n'}
[1.193387] (slpmu_ros2) StdoutLine: {'line': b'[ 65%] Built target slpmu_ros2\n'}
[1.209401] (-) TimerEvent: {}
[1.221369] (slpmu_ros2) StdoutLine: {'line': b'[ 68%] Built target slpmu_ros2__py\n'}
[1.251193] (slpmu_ros2) StdoutLine: {'line': b'[ 73%] Built target slpmu_ros2__rosidl_generator_py\n'}
[1.304316] (slpmu_ros2) StdoutLine: {'line': b'[ 78%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c\n'}
[1.308186] (slpmu_ros2) StdoutLine: {'line': b'[ 84%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c\n'}
[1.310149] (-) TimerEvent: {}
[1.335716] (slpmu_ros2) StdoutLine: {'line': b'[ 89%] Built target slpmu_ros2_s__rosidl_typesupport_c\n'}
[1.361591] (slpmu_ros2) StdoutLine: {'line': b'[ 92%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o\x1b[0m\n'}
[1.362755] (slpmu_ros2) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o\x1b[0m\n'}
[1.403792] (slpmu_ros2) StdoutLine: {'line': b'running egg_info\n'}
[1.410243] (-) TimerEvent: {}
[1.469427] (slpmu_ros2) StdoutLine: {'line': b'writing slpmu_ros2.egg-info/PKG-INFO\n'}
[1.470020] (slpmu_ros2) StdoutLine: {'line': b'writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt\n'}
[1.470449] (slpmu_ros2) StdoutLine: {'line': b'writing top-level names to slpmu_ros2.egg-info/top_level.txt\n'}
[1.510463] (-) TimerEvent: {}
[1.611253] (-) TimerEvent: {}
[1.711994] (-) TimerEvent: {}
[1.738377] (slpmu_ros2) StdoutLine: {'line': b"reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'\n"}
[1.738898] (slpmu_ros2) StdoutLine: {'line': b"writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'\n"}
[1.770086] (slpmu_ros2) StdoutLine: {'line': b'[ 94%] Built target ament_cmake_python_build_slpmu_ros2_egg\n'}
[1.813156] (-) TimerEvent: {}
[1.913478] (-) TimerEvent: {}
[2.013811] (-) TimerEvent: {}
[2.114164] (-) TimerEvent: {}
[2.214513] (-) TimerEvent: {}
[2.314860] (-) TimerEvent: {}
[2.415555] (-) TimerEvent: {}
[2.517173] (-) TimerEvent: {}
[2.617597] (-) TimerEvent: {}
[2.718636] (-) TimerEvent: {}
[2.818912] (-) TimerEvent: {}
[2.919415] (-) TimerEvent: {}
[3.019975] (-) TimerEvent: {}
[3.120499] (-) TimerEvent: {}
[3.221077] (-) TimerEvent: {}
[3.321670] (-) TimerEvent: {}
[3.422228] (-) TimerEvent: {}
[3.523185] (-) TimerEvent: {}
[3.624653] (-) TimerEvent: {}
[3.725292] (-) TimerEvent: {}
[3.828417] (-) TimerEvent: {}
[3.928831] (-) TimerEvent: {}
[4.029250] (-) TimerEvent: {}
[4.129652] (-) TimerEvent: {}
[4.231259] (-) TimerEvent: {}
[4.331648] (-) TimerEvent: {}
[4.432049] (-) TimerEvent: {}
[4.532505] (-) TimerEvent: {}
[4.595836] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid slpmu::node::PMUNode::\x1b[01;32m\x1b[Kinit_operators\x1b[m\x1b[K()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[4.596440] (slpmu_ros2) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:201:29:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kon_alarm_status\x1b[m\x1b[K\xe2\x80\x99 is not a member of \xe2\x80\x98\x1b[01m\x1b[Kslpmu::node::PMUNode\x1b[m\x1b[K\xe2\x80\x99\n'}
[4.596879] (slpmu_ros2) StderrLine: {'line': b'  201 |         std::bind(&PMUNode::\x1b[01;31m\x1b[Kon_alarm_status\x1b[m\x1b[K, this, std::placeholders::_1));\n'}
[4.597323] (slpmu_ros2) StderrLine: {'line': b'      |                             \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[4.632630] (-) TimerEvent: {}
[4.733190] (-) TimerEvent: {}
[4.833525] (-) TimerEvent: {}
[4.934193] (-) TimerEvent: {}
[5.034550] (-) TimerEvent: {}
[5.134902] (-) TimerEvent: {}
[5.236777] (-) TimerEvent: {}
[5.337041] (-) TimerEvent: {}
[5.440316] (-) TimerEvent: {}
[5.540602] (-) TimerEvent: {}
[5.640887] (-) TimerEvent: {}
[5.741204] (-) TimerEvent: {}
[5.841499] (-) TimerEvent: {}
[5.941792] (-) TimerEvent: {}
[6.042105] (-) TimerEvent: {}
[6.142401] (-) TimerEvent: {}
[6.242710] (-) TimerEvent: {}
[6.343027] (-) TimerEvent: {}
[6.443432] (-) TimerEvent: {}
[6.543718] (-) TimerEvent: {}
[6.643998] (-) TimerEvent: {}
[6.744262] (-) TimerEvent: {}
[6.844562] (-) TimerEvent: {}
[6.944930] (-) TimerEvent: {}
[7.045243] (-) TimerEvent: {}
[7.145556] (-) TimerEvent: {}
[7.245858] (-) TimerEvent: {}
[7.346180] (-) TimerEvent: {}
[7.446472] (-) TimerEvent: {}
[7.546770] (-) TimerEvent: {}
[7.647092] (-) TimerEvent: {}
[7.747384] (-) TimerEvent: {}
[7.847672] (-) TimerEvent: {}
[7.947965] (-) TimerEvent: {}
[8.048203] (-) TimerEvent: {}
[8.148472] (-) TimerEvent: {}
[8.248775] (-) TimerEvent: {}
[8.349041] (-) TimerEvent: {}
[8.449326] (-) TimerEvent: {}
[8.549625] (-) TimerEvent: {}
[8.649923] (-) TimerEvent: {}
[8.670450] (slpmu_ros2) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:76: CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o] Error 1\n'}
[8.670925] (slpmu_ros2) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/slpmu_node.dir/all] Error 2\n'}
[8.671109] (slpmu_ros2) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[8.673397] (slpmu_ros2) CommandEnded: {'returncode': 2}
[8.686469] (slpmu_ros2) JobEnded: {'identifier': 'slpmu_ros2', 'rc': 2}
[8.696991] (-) EventReactorShutdown: {}
