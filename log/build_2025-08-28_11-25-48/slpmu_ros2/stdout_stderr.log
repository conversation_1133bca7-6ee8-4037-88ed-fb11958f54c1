[  2%] Built target slpmu_ros2__rosidl_generator_type_description
[  2%] Built target ament_cmake_python_copy_slpmu_ros2
[  5%] Built target slpmu_ros2__cpp
[ 18%] Built target slpmu_ros2__rosidl_generator_c
[ 26%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c
[ 34%] Built target slpmu_ros2__rosidl_typesupport_cpp
[ 42%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp
[ 50%] Built target slpmu_ros2__rosidl_typesupport_c
[ 57%] Built target slpmu_ros2__rosidl_typesupport_introspection_c
[ 65%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp
[ 65%] Built target slpmu_ros2
[ 68%] Built target slpmu_ros2__py
[ 73%] Built target slpmu_ros2__rosidl_generator_py
[ 78%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c
[ 84%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c
[ 89%] Built target slpmu_ros2_s__rosidl_typesupport_c
[ 92%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[ 94%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
running egg_info
writing slpmu_ros2.egg-info/PKG-INFO
writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt
writing top-level names to slpmu_ros2.egg-info/top_level.txt
reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[ 94%] Built target ament_cmake_python_build_slpmu_ros2_egg
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:[m[K In member function ‘[01m[Kvoid slpmu::node::PMUNode::[01;32m[Kinit_operators[m[K()[m[K’:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:201:29:[m[K [01;31m[Kerror: [m[K‘[01m[Kon_alarm_status[m[K’ is not a member of ‘[01m[Kslpmu::node::PMUNode[m[K’
  201 |         std::bind(&PMUNode::[01;31m[Kon_alarm_status[m[K, this, std::placeholders::_1));
      |                             [01;31m[K^~~~~~~~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:76: CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/slpmu_node.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
