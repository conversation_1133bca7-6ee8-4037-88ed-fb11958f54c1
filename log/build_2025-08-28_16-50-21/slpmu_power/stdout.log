[ 50%] Built target slpmu_power
[100%] Built target power_utils
-- Install configuration: ""
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/libslpmu_power.a
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/slpmu_power.hpp
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/i_battery.hpp
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/battery_can.hpp
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets.cmake
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets-noconfig.cmake
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/slpmu_power-config.cmake
-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin/power_utils
