[0.000000] (-) TimerEvent: {}
[0.001095] (sl_vcu_all) JobQueued: {'identifier': 'sl_vcu_all', 'dependencies': OrderedDict()}
[0.001186] (slcan) JobQueued: {'identifier': 'slcan', 'dependencies': OrderedDict()}
[0.001352] (slpmu_process) JobQueued: {'identifier': 'slpmu_process', 'dependencies': OrderedDict()}
[0.001477] (slpmu_jack) JobQueued: {'identifier': 'slpmu_jack', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001561] (slpmu_motor) JobQueued: {'identifier': 'slpmu_motor', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001587] (slpmu_power) JobQueued: {'identifier': 'slpmu_power', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001607] (slpmu_ros2) JobQueued: {'identifier': 'slpmu_ros2', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan', 'slpmu_process': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process', 'slpmu_jack': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack', 'slpmu_motor': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor'})}
[0.001644] (slcan) JobStarted: {'identifier': 'slcan'}
[0.008573] (slpmu_process) JobStarted: {'identifier': 'slpmu_process'}
[0.012576] (sl_vcu_all) JobStarted: {'identifier': 'sl_vcu_all'}
[0.018150] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'cmake'}
[0.018919] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'build'}
[0.019840] (slcan) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.022104] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'cmake'}
[0.022684] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'build'}
[0.023240] (slpmu_process) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.026054] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'cmake'}
[0.026869] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'build'}
[0.027528] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.101200] (-) TimerEvent: {}
[0.103499] (slcan) StdoutLine: {'line': b'[100%] Built target slcan\n'}
[0.105477] (slpmu_process) StdoutLine: {'line': b'[100%] Built target slpmu_process\n'}
[0.109376] (sl_vcu_all) StdoutLine: {'line': b'[  0%] Built target sl_vcu_all__rosidl_generator_type_description\n'}
[0.120626] (slcan) CommandEnded: {'returncode': 0}
[0.121253] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'install'}
[0.135941] (slcan) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.141526] (slpmu_process) CommandEnded: {'returncode': 0}
[0.144665] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'install'}
[0.144966] (slpmu_process) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.148284] (sl_vcu_all) StdoutLine: {'line': b'[  1%] Built target sl_vcu_all__cpp\n'}
[0.152562] (slcan) StdoutLine: {'line': b'-- Install configuration: "RelWithDebInfo"\n'}
[0.152948] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a\n'}
[0.153215] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include\n'}
[0.153464] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan\n'}
[0.153694] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan.hpp\n'}
[0.153892] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan\n'}
[0.154155] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/canopen.hpp\n'}
[0.154501] (sl_vcu_all) StdoutLine: {'line': b'[  1%] Built target ament_cmake_python_copy_sl_vcu_all\n'}
[0.154769] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/socket_can.hpp\n'}
[0.154882] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets.cmake\n'}
[0.155395] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets-relwithdebinfo.cmake\n'}
[0.155528] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/slcan-config.cmake\n'}
[0.159210] (slcan) CommandEnded: {'returncode': 0}
[0.159531] (sl_vcu_all) StdoutLine: {'line': b'[ 25%] Built target sl_vcu_all__rosidl_generator_c\n'}
[0.161921] (slpmu_process) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.162271] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/libslpmu_process.a\n'}
[0.162883] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include\n'}
[0.163025] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu\n'}
[0.163125] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/process\n'}
[0.163240] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/process/imu_odom_fusion.hpp\n'}
[0.163334] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/slpmu_process.hpp\n'}
[0.163943] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/cmake/slpmu_process_targets.cmake\n'}
[0.164082] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/cmake/slpmu_process_targets-release.cmake\n'}
[0.164198] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/slpmu_process-config.cmake\n'}
[0.177163] (slcan) JobEnded: {'identifier': 'slcan', 'rc': 0}
[0.179616] (slpmu_jack) JobStarted: {'identifier': 'slpmu_jack'}
[0.185583] (slpmu_motor) JobStarted: {'identifier': 'slpmu_motor'}
[0.190952] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'cmake'}
[0.191857] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'build'}
[0.192101] (slpmu_jack) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.194454] (slpmu_process) CommandEnded: {'returncode': 0}
[0.201276] (-) TimerEvent: {}
[0.208239] (slpmu_process) JobEnded: {'identifier': 'slpmu_process', 'rc': 0}
[0.208951] (slpmu_power) JobStarted: {'identifier': 'slpmu_power'}
[0.215795] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'cmake'}
[0.215844] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'build'}
[0.215867] (slpmu_motor) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.227969] (sl_vcu_all) StdoutLine: {'line': b'[ 34%] Built target sl_vcu_all__rosidl_typesupport_cpp\n'}
[0.230766] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'cmake'}
[0.230796] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'build'}
[0.230817] (slpmu_power) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.234046] (sl_vcu_all) StdoutLine: {'line': b'[ 42%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_c\n'}
[0.251585] (sl_vcu_all) StdoutLine: {'line': b'[ 51%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_cpp\n'}
[0.281284] (sl_vcu_all) StdoutLine: {'line': b'[ 59%] Built target sl_vcu_all__rosidl_typesupport_introspection_c\n'}
[0.290672] (slpmu_jack) StdoutLine: {'line': b'[ 50%] Built target slpmu_jack\n'}
[0.300844] (sl_vcu_all) StdoutLine: {'line': b'[ 68%] Built target sl_vcu_all__rosidl_typesupport_c\n'}
[0.304657] (-) TimerEvent: {}
[0.315927] (sl_vcu_all) StdoutLine: {'line': b'[ 77%] Built target sl_vcu_all__rosidl_typesupport_introspection_cpp\n'}
[0.353345] (slpmu_jack) StdoutLine: {'line': b'[100%] Built target jack_utils\n'}
[0.357249] (slpmu_motor) StdoutLine: {'line': b'[ 50%] Built target slpmu_motor\n'}
[0.357847] (sl_vcu_all) StdoutLine: {'line': b'[ 78%] Built target can_frame_dispatcher_node\n'}
[0.361226] (slpmu_power) StdoutLine: {'line': b'[ 50%] Built target slpmu_power\n'}
[0.381372] (slpmu_jack) CommandEnded: {'returncode': 0}
[0.382108] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'install'}
[0.382365] (slpmu_jack) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.383016] (sl_vcu_all) StdoutLine: {'line': b'[ 79%] Built target zl_motor_controller_node\n'}
[0.391383] (slpmu_motor) StdoutLine: {'line': b'[100%] Built target motor_utils\n'}
[0.406090] (-) TimerEvent: {}
[0.413264] (slpmu_motor) CommandEnded: {'returncode': 0}
[0.413631] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'install'}
[0.413661] (slpmu_motor) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.414066] (slpmu_jack) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.414682] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin/jack_utils\n'}
[0.414795] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/libslpmu_jack.a\n'}
[0.414888] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include\n'}
[0.414981] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu\n'}
[0.415067] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack\n'}
[0.415178] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/i_jack_controller.hpp\n'}
[0.416176] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp\n'}
[0.416319] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/slpmu_jack.hpp\n'}
[0.416421] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets.cmake\n'}
[0.416514] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets-release.cmake\n'}
[0.416603] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/slpmu_jack-config.cmake\n'}
[0.419831] (slpmu_jack) CommandEnded: {'returncode': 0}
[0.420377] (sl_vcu_all) StdoutLine: {'line': b'[ 81%] Built target zl_motor_modbus_controller_node\n'}
[0.423934] (slpmu_power) StdoutLine: {'line': b'[100%] Built target power_utils\n'}
[0.440027] (sl_vcu_all) StdoutLine: {'line': b'[ 82%] Built target bumper_sensor_node\n'}
[0.440293] (slpmu_motor) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.440409] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/libslpmu_motor.a\n'}
[0.440525] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include\n'}
[0.440655] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu\n'}
[0.440759] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor\n'}
[0.440848] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/i_motor_controller.hpp\n'}
[0.440944] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/motor_controller_can.hpp\n'}
[0.441036] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/slpmu_motor.hpp\n'}
[0.441127] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets.cmake\n'}
[0.441229] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets-release.cmake\n'}
[0.441318] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/slpmu_motor-config.cmake\n'}
[0.442435] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin/motor_utils\n'}
[0.446451] (slpmu_jack) JobEnded: {'identifier': 'slpmu_jack', 'rc': 0}
[0.447804] (slpmu_motor) CommandEnded: {'returncode': 0}
[0.457326] (sl_vcu_all) StdoutLine: {'line': b'[ 83%] Built target imu_sensor_node\n'}
[0.467516] (slpmu_motor) JobEnded: {'identifier': 'slpmu_motor', 'rc': 0}
[0.469185] (slpmu_ros2) JobStarted: {'identifier': 'slpmu_ros2'}
[0.474837] (slpmu_power) CommandEnded: {'returncode': 0}
[0.475563] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'install'}
[0.476048] (slpmu_power) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.493163] (sl_vcu_all) StdoutLine: {'line': b'[ 84%] Built target teleop_key\n'}
[0.494852] (slpmu_power) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.495036] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/libslpmu_power.a\n'}
[0.495149] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include\n'}
[0.495249] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu\n'}
[0.495868] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/slpmu_power.hpp\n'}
[0.496693] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power\n'}
[0.496844] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/i_battery.hpp\n'}
[0.496944] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/battery_can.hpp\n'}
[0.498175] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets.cmake\n'}
[0.498321] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets-noconfig.cmake\n'}
[0.498420] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/slpmu_power-config.cmake\n'}
[0.500542] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin/power_utils\n'}
[0.501306] (sl_vcu_all) StdoutLine: {'line': b'[ 85%] Built target battery_monitor_node\n'}
[0.503227] (slpmu_power) CommandEnded: {'returncode': 0}
[0.506179] (-) TimerEvent: {}
[0.519488] (slpmu_power) JobEnded: {'identifier': 'slpmu_power', 'rc': 0}
[0.520432] (slpmu_ros2) JobProgress: {'identifier': 'slpmu_ros2', 'progress': 'cmake'}
[0.521375] (slpmu_ros2) JobProgress: {'identifier': 'slpmu_ros2', 'progress': 'build'}
[0.521770] (slpmu_ros2) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.523820] (sl_vcu_all) StdoutLine: {'line': b'[ 87%] Built target jack_control_node\n'}
[0.532402] (sl_vcu_all) StdoutLine: {'line': b'[ 87%] Built target sl_vcu_all\n'}
[0.547379] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target led_display_control_node\n'}
[0.553259] (sl_vcu_all) StdoutLine: {'line': b'running egg_info\n'}
[0.561447] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target sl_vcu_all__py\n'}
[0.593861] (sl_vcu_all) StdoutLine: {'line': b'writing sl_vcu_all.egg-info/PKG-INFO\n'}
[0.594110] (sl_vcu_all) StdoutLine: {'line': b'writing dependency_links to sl_vcu_all.egg-info/dependency_links.txt\n'}
[0.594381] (sl_vcu_all) StdoutLine: {'line': b'writing top-level names to sl_vcu_all.egg-info/top_level.txt\n'}
[0.604985] (sl_vcu_all) StdoutLine: {'line': b'[ 96%] Built target sl_vcu_all__rosidl_generator_py\n'}
[0.607204] (-) TimerEvent: {}
[0.613238] (slpmu_ros2) StdoutLine: {'line': b'[  2%] Built target slpmu_ros2__rosidl_generator_type_description\n'}
[0.640224] (slpmu_ros2) StdoutLine: {'line': b'[  2%] Built target ament_cmake_python_copy_slpmu_ros2\n'}
[0.670292] (slpmu_ros2) StdoutLine: {'line': b'[  4%] Built target slpmu_ros2__cpp\n'}
[0.673718] (sl_vcu_all) StdoutLine: {'line': b'[ 97%] Built target sl_vcu_all_s__rosidl_typesupport_c\n'}
[0.690612] (slpmu_ros2) StdoutLine: {'line': b'[ 20%] Built target slpmu_ros2__rosidl_generator_c\n'}
[0.698227] (sl_vcu_all) StdoutLine: {'line': b'[ 99%] Built target sl_vcu_all_s__rosidl_typesupport_fastrtps_c\n'}
[0.707927] (-) TimerEvent: {}
[0.737692] (sl_vcu_all) StdoutLine: {'line': b"reading manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[0.739375] (sl_vcu_all) StdoutLine: {'line': b"writing manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[0.744294] (slpmu_ros2) StdoutLine: {'line': b'[ 28%] Built target slpmu_ros2__rosidl_typesupport_cpp\n'}
[0.764183] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target sl_vcu_all_s__rosidl_typesupport_introspection_c\n'}
[0.767855] (slpmu_ros2) StdoutLine: {'line': b'[ 36%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp\n'}
[0.790993] (slpmu_ros2) StdoutLine: {'line': b'[ 44%] Built target slpmu_ros2__rosidl_typesupport_introspection_c\n'}
[0.795649] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_sl_vcu_all_egg\n'}
[0.808012] (-) TimerEvent: {}
[0.845256] (sl_vcu_all) CommandEnded: {'returncode': 0}
[0.846850] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'install'}
[0.846926] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.870619] (slpmu_ros2) StdoutLine: {'line': b'[ 53%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp\n'}
[0.876881] (slpmu_ros2) StdoutLine: {'line': b'[ 61%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c\n'}
[0.885335] (sl_vcu_all) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.885524] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/rosidl_interfaces/sl_vcu_all\n'}
[0.885629] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.json\n'}
[0.885726] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.json\n'}
[0.885820] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.json\n'}
[0.885913] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.json\n'}
[0.886002] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.json\n'}
[0.886089] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.json\n'}
[0.886187] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.json\n'}
[0.886275] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.json\n'}
[0.886373] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.json\n'}
[0.886467] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.json\n'}
[0.886555] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.json\n'}
[0.886643] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.json\n'}
[0.886728] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.886819] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.886908] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.886999] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.h\n'}
[0.887082] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.h\n'}
[0.887190] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.h\n'}
[0.887281] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.c\n'}
[0.887369] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[0.887474] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__description.c\n'}
[0.887562] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.h\n'}
[0.887648] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.887735] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.h\n'}
[0.887825] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.890287] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.h\n'}
[0.890406] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.h\n'}
[0.890502] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.c\n'}
[0.890596] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.h\n'}
[0.890688] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[0.908102] (-) TimerEvent: {}
[0.958290] (slpmu_ros2) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/jack_operator.cpp.o\x1b[0m\n'}
[0.958601] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[0.958726] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.h\n'}
[0.958817] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__description.c\n'}
[0.958903] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.h\n'}
[0.958989] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.c\n'}
[0.959074] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[0.959172] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.c\n'}
[0.959273] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__description.c\n'}
[0.959366] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__description.c\n'}
[0.959449] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.h\n'}
[0.959532] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.h\n'}
[0.959614] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.h\n'}
[0.959696] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.h\n'}
[0.959780] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.h\n'}
[0.959862] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.h\n'}
[0.959945] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.960028] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.h\n'}
[0.960111] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.h\n'}
[0.961882] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.h\n'}
[0.962247] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.h\n'}
[0.962500] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_c__visibility_control.h\n'}
[0.962751] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.h\n'}
[0.963013] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.963279] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.h\n'}
[0.963373] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__description.c\n'}
[0.963458] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[0.963595] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__description.c\n'}
[0.963681] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.c\n'}
[0.963768] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.c\n'}
[0.963981] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.h\n'}
[0.964087] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[0.964182] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.h\n'}
[0.964267] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[0.964352] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.h\n'}
[0.964437] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.h\n'}
[0.964521] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.h\n'}
[0.964618] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.c\n'}
[0.964702] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.h\n'}
[0.964783] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[0.964863] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[0.964944] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.h\n'}
[0.965024] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[0.965106] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__description.c\n'}
[0.965204] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__description.c\n'}
[0.965289] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.h\n'}
[0.965372] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.h\n'}
[0.965589] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.h\n'}
[0.965687] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.c\n'}
[0.965820] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.h\n'}
[0.965926] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.h\n'}
[0.966076] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.h\n'}
[0.966184] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.h\n'}
[0.966433] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[0.966548] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[0.966700] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__description.c\n'}
[0.966839] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.h\n'}
[0.967026] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.c\n'}
[0.967116] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.h\n'}
[0.967208] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.c\n'}
[0.967291] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__description.c\n'}
[0.967373] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.h\n'}
[0.967455] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__description.c\n'}
[0.967537] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.c\n'}
[0.967628] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__description.c\n'}
[0.967711] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.h\n'}
[0.967794] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.h\n'}
[0.967877] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.h\n'}
[0.967958] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.h\n'}
[0.968040] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.c\n'}
[0.968122] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.h\n'}
[0.968219] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.h\n'}
[0.968303] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.h\n'}
[0.968387] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.h\n'}
[0.968480] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.h\n'}
[0.968566] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.sh\n'}
[0.968649] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.dsv\n'}
[0.968731] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_c.so\n'}
[0.968813] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.968894] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.968974] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.969070] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_c.h\n'}
[0.969164] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.969313] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.969403] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_c.h\n'}
[0.969528] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_c.h\n'}
[0.969646] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.969741] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.969882] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.970084] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.970191] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_c.h\n'}
[0.970277] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.970361] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.970451] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.970536] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_c.h\n'}
[0.970619] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_c.h\n'}
[0.970708] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_c.h\n'}
[0.970789] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.970871] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_c.so\n'}
[0.970953] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.971035] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.971118] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.971214] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__builder.hpp\n'}
[0.971300] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__traits.hpp\n'}
[0.971382] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.hpp\n'}
[0.971464] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.hpp\n'}
[0.971549] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.hpp\n'}
[0.971631] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.971712] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.hpp\n'}
[0.971793] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.971889] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.hpp\n'}
[0.971972] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__builder.hpp\n'}
[0.972053] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__builder.hpp\n'}
[0.972141] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.hpp\n'}
[0.972233] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__builder.hpp\n'}
[0.972314] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.hpp\n'}
[0.972395] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__traits.hpp\n'}
[0.972478] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__traits.hpp\n'}
[0.972567] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.hpp\n'}
[0.972649] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__traits.hpp\n'}
[0.972736] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.hpp\n'}
[0.972819] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.hpp\n'}
[0.972904] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.hpp\n'}
[0.972985] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.hpp\n'}
[0.973066] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.973153] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.hpp\n'}
[0.973243] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.hpp\n'}
[0.973325] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.hpp\n'}
[0.973449] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.hpp\n'}
[0.973532] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.973621] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__traits.hpp\n'}
[0.973704] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.hpp\n'}
[0.973833] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__builder.hpp\n'}
[0.973920] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__traits.hpp\n'}
[0.974025] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__traits.hpp\n'}
[0.974310] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.hpp\n'}
[0.974577] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__builder.hpp\n'}
[0.974856] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__traits.hpp\n'}
[0.975107] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__builder.hpp\n'}
[0.975211] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.hpp\n'}
[0.975319] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__traits.hpp\n'}
[0.975449] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__traits.hpp\n'}
[0.975533] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__builder.hpp\n'}
[0.975624] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__builder.hpp\n'}
[0.975708] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__traits.hpp\n'}
[0.975807] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.hpp\n'}
[0.975921] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.hpp\n'}
[0.976006] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.hpp\n'}
[0.976174] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.hpp\n'}
[0.976266] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.hpp\n'}
[0.976350] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.hpp\n'}
[0.976432] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__builder.hpp\n'}
[0.976514] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.hpp\n'}
[0.976597] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__traits.hpp\n'}
[0.976685] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.hpp\n'}
[0.976768] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.hpp\n'}
[0.976853] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.hpp\n'}
[0.976936] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.hpp\n'}
[0.977018] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.hpp\n'}
[0.977100] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__builder.hpp\n'}
[0.977203] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__builder.hpp\n'}
[0.977296] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.hpp\n'}
[0.977379] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.hpp\n'}
[0.977461] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.hpp\n'}
[0.977543] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.hpp\n'}
[0.977625] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.977707] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.hpp\n'}
[0.977789] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.977870] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.977952] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.978037] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/dds_fastrtps\n'}
[0.978136] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.978230] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.978312] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.978395] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/dds_fastrtps\n'}
[0.978477] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.978558] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.978644] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.978728] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.978810] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.978894] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/dds_fastrtps\n'}
[0.978976] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.979058] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.979154] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.979328] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.979423] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.979562] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.979684] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.979780] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.979922] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.980030] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.980299] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.980604] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.980884] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.981170] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_c.h\n'}
[0.981293] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[0.981379] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.981464] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.981547] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_c.h\n'}
[0.981646] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[0.981731] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[0.981813] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_c.h\n'}
[0.981898] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[0.981986] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_c.h\n'}
[0.982075] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.982169] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.982258] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.982340] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[0.982423] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_c.h\n'}
[0.982504] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_c.h\n'}
[0.982586] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[0.982669] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[0.982752] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_c.h\n'}
[0.982836] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_c.h\n'}
[0.982921] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[0.983004] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[0.983086] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[0.983181] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[0.983270] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[0.983353] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_c.h\n'}
[0.983438] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_c.h\n'}
[0.983523] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_c.h\n'}
[0.983606] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_c.h\n'}
[0.983688] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_c.so\n'}
[0.983778] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_c.so\n'}
[0.983859] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.983941] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.984034] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.984118] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.984216] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.cpp\n'}
[0.984306] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.984390] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.984472] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.cpp\n'}
[0.984554] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.cpp\n'}
[0.984638] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.984719] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.cpp\n'}
[0.984800] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.984882] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.984964] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.985045] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.985127] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.cpp\n'}
[0.985342] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.985429] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.cpp\n'}
[0.985513] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.cpp\n'}
[0.985605] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.cpp\n'}
[0.985689] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.985787] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.985871] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.985952] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.986035] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.986120] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.cpp\n'}
[0.986217] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.cpp\n'}
[0.986301] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.986383] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.cpp\n'}
[0.986465] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.986560] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.cpp\n'}
[0.986647] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_cpp.so\n'}
[0.986731] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_cpp.so\n'}
[0.986813] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.sh\n'}
[0.986894] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.dsv\n'}
[0.986977] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info\n'}
[0.987060] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/SOURCES.txt\n'}
[0.987149] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/PKG-INFO\n'}
[0.987241] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/top_level.txt\n'}
[0.987324] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/dependency_links.txt\n'}
[0.987405] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all\n'}
[0.987495] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.987577] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[0.987659] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_c.c\n'}
[0.987740] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action\n'}
[0.987823] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/__init__.py\n'}
[0.987905] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control.py\n'}
[0.987987] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control_s.c\n'}
[0.988069] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.989181] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/__init__.py\n'}
[0.989291] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv\n'}
[0.989378] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status_s.c\n'}
[0.989460] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/__init__.py\n'}
[0.989541] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control.py\n'}
[0.989623] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control_s.c\n'}
[0.989706] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status.py\n'}
[0.989788] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter.py\n'}
[0.989873] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter_s.c\n'}
[0.989956] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[0.990057] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg\n'}
[0.990148] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info.py\n'}
[0.990239] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame_s.c\n'}
[1.003292] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data_s.c\n'}
[1.003410] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info_s.c\n'}
[1.003501] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data.py\n'}
[1.003585] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status_s.c\n'}
[1.003669] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status.py\n'}
[1.003752] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state_s.c\n'}
[1.003836] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/__init__.py\n'}
[1.003919] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status_s.c\n'}
[1.004002] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state.py\n'}
[1.004085] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status.py\n'}
[1.007240] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame.py\n'}
[1.007349] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state.py\n'}
[1.007435] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data.py\n'}
[1.007517] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state_s.c\n'}
[1.007600] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data_s.c\n'}
[1.007683] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[1.010160] (-) TimerEvent: {}
[1.066787] (slpmu_ros2) StdoutLine: {'line': b'[ 71%] Built target slpmu_ros2__rosidl_typesupport_c\n'}
[1.073273] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all'...\n"}
[1.073450] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action'...\n"}
[1.073547] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg'...\n"}
[1.073635] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv'...\n"}
[1.098415] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[1.099741] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[1.100342] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[1.101458] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_py.so\n'}
[1.101876] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.idl\n'}
[1.102536] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.idl\n'}
[1.103730] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.idl\n'}
[1.104346] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.idl\n'}
[1.105011] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.idl\n'}
[1.109419] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.idl\n'}
[1.109947] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.idl\n'}
[1.112861] (-) TimerEvent: {}
[1.113227] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.idl\n'}
[1.116198] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.idl\n'}
[1.116702] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.idl\n'}
[1.117105] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.idl\n'}
[1.117504] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.idl\n'}
[1.117966] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.srv\n'}
[1.118361] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.srv\n'}
[1.118748] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.srv\n'}
[1.119140] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.msg\n'}
[1.119525] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.msg\n'}
[1.119911] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.msg\n'}
[1.120303] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.msg\n'}
[1.121023] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.msg\n'}
[1.121452] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.msg\n'}
[1.121842] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.msg\n'}
[1.122237] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.msg\n'}
[1.123252] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.action\n'}
[1.125305] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/can_frame_dispatcher_node\n'}
[1.125430] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_controller_node\n'}
[1.125529] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_modbus_controller_node\n'}
[1.125621] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/bumper_sensor_node\n'}
[1.125714] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/imu_sensor_node\n'}
[1.125804] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/teleop_key\n'}
[1.125896] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/battery_monitor_node\n'}
[1.125987] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/jack_control_node\n'}
[1.126073] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/led_display_control_node\n'}
[1.126195] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch\n'}
[1.126284] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/battery_monitor.launch.py\n'}
[1.126385] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/jack_control.launch.py\n'}
[1.126473] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/robot_localization_ekf.launch.py\n'}
[1.126562] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_controller.launch.py\n'}
[1.126646] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_modbus_controller.launch.py\n'}
[1.126734] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/led_control.launch.py\n'}
[1.126835] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/vcu_nodes.launch.py\n'}
[1.126923] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/imu_sensor.launch.py\n'}
[1.127009] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/bumper_sensor.launch.py\n'}
[1.127096] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/sl_vcu_all.launch.py\n'}
[1.127193] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config\n'}
[1.127282] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/ekf.yaml\n'}
[1.127371] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_modbus_controller.yaml\n'}
[1.127458] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/battery_monitor.yaml\n'}
[1.127554] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_controller.yaml\n'}
[1.127637] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/jack_control.yaml\n'}
[1.127726] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/imu_sensor.yaml\n'}
[1.127810] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/robot_localization_ekf.yaml\n'}
[1.127902] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/bumper_sensor.yaml\n'}
[1.127991] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/led_control.yaml\n'}
[1.128080] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/package_run_dependencies/sl_vcu_all\n'}
[1.134299] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/parent_prefix_path/sl_vcu_all\n'}
[1.134436] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.sh\n'}
[1.134536] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.dsv\n'}
[1.134629] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.sh\n'}
[1.134721] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.dsv\n'}
[1.134811] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.bash\n'}
[1.134899] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.sh\n'}
[1.134984] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.zsh\n'}
[1.135070] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.dsv\n'}
[1.135164] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv\n'}
[1.135251] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/packages/sl_vcu_all\n'}
[1.135338] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport.cmake\n'}
[1.135435] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport-noconfig.cmake\n'}
[1.135525] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[1.135613] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[1.135701] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cppExport.cmake\n'}
[1.135791] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[1.135880] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[1.135978] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport.cmake\n'}
[1.136068] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[1.136166] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport.cmake\n'}
[1.136255] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport-noconfig.cmake\n'}
[1.136340] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport.cmake\n'}
[1.136428] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[1.136512] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport.cmake\n'}
[1.136599] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[1.136687] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport.cmake\n'}
[1.136774] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport-noconfig.cmake\n'}
[1.136861] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake-extras.cmake\n'}
[1.136947] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[1.137033] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[1.137118] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[1.142331] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_targets-extras.cmake\n'}
[1.142444] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[1.142541] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[1.142632] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig.cmake\n'}
[1.142735] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig-version.cmake\n'}
[1.142824] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.xml\n'}
[1.153647] (slpmu_ros2) StdoutLine: {'line': b'[ 71%] Built target slpmu_ros2\n'}
[1.185180] (sl_vcu_all) CommandEnded: {'returncode': 0}
[1.193295] (slpmu_ros2) StdoutLine: {'line': b'[ 73%] Built target slpmu_ros2__py\n'}
[1.218387] (-) TimerEvent: {}
[1.222514] (slpmu_ros2) StdoutLine: {'line': b'[ 79%] Built target slpmu_ros2__rosidl_generator_py\n'}
[1.227515] (sl_vcu_all) JobEnded: {'identifier': 'sl_vcu_all', 'rc': 0}
[1.269977] (slpmu_ros2) StdoutLine: {'line': b'[ 83%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c\n'}
[1.270519] (slpmu_ros2) StdoutLine: {'line': b'running egg_info\n'}
[1.310212] (slpmu_ros2) StdoutLine: {'line': b'[ 87%] Built target slpmu_ros2_s__rosidl_typesupport_c\n'}
[1.312919] (slpmu_ros2) StdoutLine: {'line': b'writing slpmu_ros2.egg-info/PKG-INFO\n'}
[1.314249] (slpmu_ros2) StdoutLine: {'line': b'[ 91%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c\n'}
[1.314821] (slpmu_ros2) StdoutLine: {'line': b'writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt\n'}
[1.315241] (slpmu_ros2) StdoutLine: {'line': b'writing top-level names to slpmu_ros2.egg-info/top_level.txt\n'}
[1.318482] (-) TimerEvent: {}
[1.405864] (slpmu_ros2) StdoutLine: {'line': b"reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'\n"}
[1.406628] (slpmu_ros2) StdoutLine: {'line': b"writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'\n"}
[1.418586] (-) TimerEvent: {}
[1.455821] (slpmu_ros2) StdoutLine: {'line': b'[ 91%] Built target ament_cmake_python_build_slpmu_ros2_egg\n'}
[1.518699] (-) TimerEvent: {}
[1.619188] (-) TimerEvent: {}
[1.719696] (-) TimerEvent: {}
