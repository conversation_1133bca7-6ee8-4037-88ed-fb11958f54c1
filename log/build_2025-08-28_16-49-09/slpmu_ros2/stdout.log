-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
-- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
-- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
-- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
-- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
-- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
-- Found rosidl_default_generators: 1.6.0 (/opt/ros/jazzy/share/rosidl_default_generators/cmake)
-- Found rosidl_adapter: 4.6.5 (/opt/ros/jazzy/share/rosidl_adapter/cmake)
-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
-- Found ament_cmake_ros: 0.12.0 (/opt/ros/jazzy/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found python_cmake_module: 0.11.1 (/opt/ros/jazzy/share/python_cmake_module/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Configured 'flake8' exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done (1.9s)
-- Generating done (0.1s)
-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[  2%] Built target slpmu_ros2__rosidl_generator_type_description
[  2%] Built target ament_cmake_python_copy_slpmu_ros2
[  4%] Built target slpmu_ros2__cpp
[  6%] [32m[1mLinking C shared library libslpmu_ros2__rosidl_generator_c.so[0m
[ 20%] Built target slpmu_ros2__rosidl_generator_c
[ 22%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_fastrtps_cpp.so[0m
[ 24%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_fastrtps_c.so[0m
[ 26%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_cpp.so[0m
[ 32%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp
[ 34%] [32m[1mLinking C shared library libslpmu_ros2__rosidl_typesupport_introspection_c.so[0m
running egg_info
[ 40%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c
[ 46%] Built target slpmu_ros2__rosidl_typesupport_cpp
writing slpmu_ros2.egg-info/PKG-INFO
writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt
writing top-level names to slpmu_ros2.egg-info/top_level.txt
[ 48%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_c.so[0m
[ 55%] Built target slpmu_ros2__rosidl_typesupport_introspection_c
[ 57%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_introspection_cpp.so[0m
reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[ 63%] Built target slpmu_ros2__rosidl_typesupport_c
[ 65%] Built target ament_cmake_python_build_slpmu_ros2_egg
[ 65%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[ 67%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
[ 69%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o[0m
[ 75%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp
[ 77%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/jack_operator.cpp.o[0m
[ 77%] Built target slpmu_ros2
[ 79%] Built target slpmu_ros2__py
[ 81%] [32m[1mLinking C shared library libslpmu_ros2__rosidl_generator_py.so[0m
[ 85%] Built target slpmu_ros2__rosidl_generator_py
[ 87%] [32m[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so[0m
[ 89%] [32m[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so[0m
[ 91%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c
[ 93%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c
[ 95%] [32m[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so[0m
[ 97%] Built target slpmu_ros2_s__rosidl_typesupport_c
