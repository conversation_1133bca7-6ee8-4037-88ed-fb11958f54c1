[0.019s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[0.052s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.193s] -- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
[0.235s] -- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
[0.246s] -- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
[0.263s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.280s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.365s] -- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
[0.367s] -- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
[0.456s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.544s] -- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
[0.562s] -- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
[0.582s] -- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
[0.618s] -- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
[0.623s] -- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
[0.770s] -- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
[0.775s] -- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
[0.776s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.779s] -- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
[0.812s] -- Found rosidl_default_generators: 1.6.0 (/opt/ros/jazzy/share/rosidl_default_generators/cmake)
[0.823s] -- Found rosidl_adapter: 4.6.5 (/opt/ros/jazzy/share/rosidl_adapter/cmake)
[0.828s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[1.192s] -- Found ament_cmake_ros: 0.12.0 (/opt/ros/jazzy/share/ament_cmake_ros/cmake)
[1.491s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.604s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.606s] -- Found python_cmake_module: 0.11.1 (/opt/ros/jazzy/share/python_cmake_module/cmake)
[1.934s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
[1.977s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.978s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include>
[1.978s] -- Configured cppcheck exclude dirs and/or files: 
[1.979s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.979s] -- Configured 'flake8' exclude dirs and/or files: 
[1.980s] -- Added test 'lint_cmake' to check CMake code style
[1.980s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.982s] -- Added test 'uncrustify' to check C / C++ code style
[1.983s] -- Configured uncrustify additional arguments: 
[1.983s] -- Added test 'xmllint' to check XML markup files
[1.988s] -- Configuring done (1.9s)
[2.062s] -- Generating done (0.1s)
[2.083s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[2.137s] [  2%] Built target slpmu_ros2__rosidl_generator_type_description
[2.158s] [  2%] Built target ament_cmake_python_copy_slpmu_ros2
[2.171s] [  4%] Built target slpmu_ros2__cpp
[2.205s] [  6%] [32m[1mLinking C shared library libslpmu_ros2__rosidl_generator_c.so[0m
[2.258s] [ 20%] Built target slpmu_ros2__rosidl_generator_c
[2.303s] [ 22%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_fastrtps_cpp.so[0m
[2.314s] [ 24%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_fastrtps_c.so[0m
[2.322s] [ 26%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_cpp.so[0m
[2.446s] [ 32%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp
[2.486s] [ 34%] [32m[1mLinking C shared library libslpmu_ros2__rosidl_typesupport_introspection_c.so[0m
[2.521s] running egg_info
[2.531s] [ 40%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c
[2.550s] [ 46%] Built target slpmu_ros2__rosidl_typesupport_cpp
[2.555s] writing slpmu_ros2.egg-info/PKG-INFO
[2.558s] writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt
[2.558s] writing top-level names to slpmu_ros2.egg-info/top_level.txt
[2.585s] [ 48%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_c.so[0m
[2.599s] [ 55%] Built target slpmu_ros2__rosidl_typesupport_introspection_c
[2.621s] [ 57%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_introspection_cpp.so[0m
[2.658s] reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[2.658s] writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[2.667s] [ 63%] Built target slpmu_ros2__rosidl_typesupport_c
[2.697s] [ 65%] Built target ament_cmake_python_build_slpmu_ros2_egg
[2.697s] [ 65%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[2.703s] [ 67%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
[2.721s] [ 69%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o[0m
[2.777s] [ 75%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp
[2.777s] [ 77%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/jack_operator.cpp.o[0m
[3.839s] [ 77%] Built target slpmu_ros2
[3.905s] [ 79%] Built target slpmu_ros2__py
[3.970s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/operators/jack_operator.cpp:[m[K In member function ‘[01m[Kvirtual std::optional<slpmu::operators::TimeWindow> slpmu::operators::JackOperator::[01;32m[Krequested_time_window[m[K(int64_t)[m[K’:
[3.971s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/operators/jack_operator.cpp:31:32:[m[K [01;31m[Kerror: [m[K‘[01m[Kremaining_time_ns[m[K’ was not declared in this scope
[3.971s]    31 |     if (next_run_ns > now_ns + [01;31m[Kremaining_time_ns[m[K) {
[3.971s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~[m[K
[4.000s] [ 81%] [32m[1mLinking C shared library libslpmu_ros2__rosidl_generator_py.so[0m
[4.130s] [ 85%] Built target slpmu_ros2__rosidl_generator_py
[4.137s] gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:118: CMakeFiles/slpmu_node.dir/src/operators/jack_operator.cpp.o] Error 1
[4.140s] gmake[2]: *** Waiting for unfinished jobs....
[4.239s] [ 87%] [32m[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so[0m
[4.280s] [ 89%] [32m[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so[0m
[4.329s] [ 91%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c
[4.363s] [ 93%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c
[4.380s] [ 95%] [32m[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so[0m
[4.427s] [ 97%] Built target slpmu_ros2_s__rosidl_typesupport_c
[26.535s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/slpmu_node.dir/all] Error 2
[26.536s] gmake: *** [Makefile:146: all] Error 2
[26.538s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
