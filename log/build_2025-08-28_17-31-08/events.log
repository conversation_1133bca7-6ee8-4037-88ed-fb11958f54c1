[0.000000] (-) TimerEvent: {}
[0.000412] (sl_vcu_all) JobQueued: {'identifier': 'sl_vcu_all', 'dependencies': OrderedDict()}
[0.001180] (slcan) JobQueued: {'identifier': 'slcan', 'dependencies': OrderedDict()}
[0.001268] (slpmu_process) JobQueued: {'identifier': 'slpmu_process', 'dependencies': OrderedDict()}
[0.001289] (slpmu_jack) JobQueued: {'identifier': 'slpmu_jack', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001394] (slpmu_motor) JobQueued: {'identifier': 'slpmu_motor', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001518] (slpmu_power) JobQueued: {'identifier': 'slpmu_power', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan'})}
[0.001538] (slpmu_ros2) JobQueued: {'identifier': 'slpmu_ros2', 'dependencies': OrderedDict({'slcan': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan', 'slpmu_process': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process', 'slpmu_jack': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack', 'slpmu_motor': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor'})}
[0.001564] (slcan) JobStarted: {'identifier': 'slcan'}
[0.008382] (slpmu_process) JobStarted: {'identifier': 'slpmu_process'}
[0.012423] (sl_vcu_all) JobStarted: {'identifier': 'sl_vcu_all'}
[0.018274] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'cmake'}
[0.019270] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'build'}
[0.019313] (slcan) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.021834] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'cmake'}
[0.022403] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'build'}
[0.022916] (slpmu_process) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.025789] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'cmake'}
[0.026625] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'build'}
[0.027105] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.091659] (slcan) StdoutLine: {'line': b'[100%] Built target slcan\n'}
[0.097538] (slpmu_process) StdoutLine: {'line': b'[100%] Built target slpmu_process\n'}
[0.099611] (-) TimerEvent: {}
[0.108266] (slcan) CommandEnded: {'returncode': 0}
[0.108913] (slcan) JobProgress: {'identifier': 'slcan', 'progress': 'install'}
[0.110579] (sl_vcu_all) StdoutLine: {'line': b'[  0%] Built target sl_vcu_all__rosidl_generator_type_description\n'}
[0.122402] (slcan) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.126455] (slpmu_process) CommandEnded: {'returncode': 0}
[0.126778] (slpmu_process) JobProgress: {'identifier': 'slpmu_process', 'progress': 'install'}
[0.126807] (slpmu_process) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.129656] (sl_vcu_all) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_sl_vcu_all\n'}
[0.134645] (slcan) StdoutLine: {'line': b'-- Install configuration: "RelWithDebInfo"\n'}
[0.135094] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/libslcan.a\n'}
[0.135225] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include\n'}
[0.136538] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan\n'}
[0.136704] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan.hpp\n'}
[0.136805] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan\n'}
[0.136898] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/canopen.hpp\n'}
[0.136986] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/include/slcan/slcan/socket_can.hpp\n'}
[0.137076] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets.cmake\n'}
[0.137163] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/cmake/slcan_targets-relwithdebinfo.cmake\n'}
[0.137254] (slcan) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/slcan-config.cmake\n'}
[0.139962] (slcan) CommandEnded: {'returncode': 0}
[0.147022] (slpmu_process) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.147246] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/libslpmu_process.a\n'}
[0.149108] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include\n'}
[0.149224] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu\n'}
[0.149323] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/process\n'}
[0.152056] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/process/imu_odom_fusion.hpp\n'}
[0.152193] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/include/slpmu/slpmu_process.hpp\n'}
[0.152290] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/cmake/slpmu_process_targets.cmake\n'}
[0.152380] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/cmake/slpmu_process_targets-release.cmake\n'}
[0.152523] (slpmu_process) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/slpmu_process-config.cmake\n'}
[0.155536] (sl_vcu_all) StdoutLine: {'line': b'[  1%] Built target sl_vcu_all__cpp\n'}
[0.163945] (sl_vcu_all) StdoutLine: {'line': b'[ 25%] Built target sl_vcu_all__rosidl_generator_c\n'}
[0.165269] (slcan) JobEnded: {'identifier': 'slcan', 'rc': 0}
[0.165990] (slpmu_jack) JobStarted: {'identifier': 'slpmu_jack'}
[0.170572] (slpmu_motor) JobStarted: {'identifier': 'slpmu_motor'}
[0.188422] (slpmu_process) CommandEnded: {'returncode': 0}
[0.195902] (slpmu_process) JobEnded: {'identifier': 'slpmu_process', 'rc': 0}
[0.196668] (slpmu_power) JobStarted: {'identifier': 'slpmu_power'}
[0.199696] (-) TimerEvent: {}
[0.202669] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'cmake'}
[0.203506] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'build'}
[0.203758] (slpmu_jack) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.206308] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'cmake'}
[0.206899] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'build'}
[0.207132] (slpmu_motor) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.212105] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'cmake'}
[0.212156] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'build'}
[0.212178] (slpmu_power) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.223753] (sl_vcu_all) StdoutLine: {'line': b'[ 34%] Built target sl_vcu_all__rosidl_typesupport_cpp\n'}
[0.227355] (sl_vcu_all) StdoutLine: {'line': b'[ 43%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_cpp\n'}
[0.235606] (sl_vcu_all) StdoutLine: {'line': b'[ 51%] Built target sl_vcu_all__rosidl_typesupport_fastrtps_c\n'}
[0.279511] (sl_vcu_all) StdoutLine: {'line': b'[ 60%] Built target sl_vcu_all__rosidl_typesupport_c\n'}
[0.302442] (-) TimerEvent: {}
[0.310475] (sl_vcu_all) StdoutLine: {'line': b'[ 68%] Built target sl_vcu_all__rosidl_typesupport_introspection_c\n'}
[0.317978] (slpmu_jack) StdoutLine: {'line': b'[ 50%] Built target slpmu_jack\n'}
[0.324096] (slpmu_motor) StdoutLine: {'line': b'[ 50%] Built target slpmu_motor\n'}
[0.324354] (sl_vcu_all) StdoutLine: {'line': b'[ 77%] Built target sl_vcu_all__rosidl_typesupport_introspection_cpp\n'}
[0.334367] (sl_vcu_all) StdoutLine: {'line': b'[ 78%] Built target can_frame_dispatcher_node\n'}
[0.369475] (slpmu_jack) StdoutLine: {'line': b'[100%] Built target jack_utils\n'}
[0.371768] (slpmu_power) StdoutLine: {'line': b'[ 50%] Built target slpmu_power\n'}
[0.383422] (sl_vcu_all) StdoutLine: {'line': b'[ 79%] Built target zl_motor_controller_node\n'}
[0.388475] (slpmu_motor) StdoutLine: {'line': b'[100%] Built target motor_utils\n'}
[0.395065] (sl_vcu_all) StdoutLine: {'line': b'[ 81%] Built target zl_motor_modbus_controller_node\n'}
[0.400506] (slpmu_jack) CommandEnded: {'returncode': 0}
[0.401314] (slpmu_jack) JobProgress: {'identifier': 'slpmu_jack', 'progress': 'install'}
[0.402096] (slpmu_jack) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.402553] (-) TimerEvent: {}
[0.413869] (slpmu_motor) CommandEnded: {'returncode': 0}
[0.414208] (slpmu_motor) JobProgress: {'identifier': 'slpmu_motor', 'progress': 'install'}
[0.414238] (slpmu_motor) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.418567] (sl_vcu_all) StdoutLine: {'line': b'[ 82%] Built target bumper_sensor_node\n'}
[0.427982] (slpmu_jack) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.428617] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin/jack_utils\n'}
[0.429465] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/libslpmu_jack.a\n'}
[0.429605] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include\n'}
[0.429707] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu\n'}
[0.430446] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack\n'}
[0.430568] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/i_jack_controller.hpp\n'}
[0.430668] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/jack/jack_controller_can.hpp\n'}
[0.430759] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/include/slpmu/slpmu_jack.hpp\n'}
[0.430845] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets.cmake\n'}
[0.430932] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/cmake/slpmu_jack_targets-release.cmake\n'}
[0.432833] (slpmu_jack) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/slpmu_jack-config.cmake\n'}
[0.433462] (slpmu_jack) CommandEnded: {'returncode': 0}
[0.435894] (slpmu_motor) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.436121] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/libslpmu_motor.a\n'}
[0.436529] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include\n'}
[0.436636] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu\n'}
[0.436724] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor\n'}
[0.436816] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/i_motor_controller.hpp\n'}
[0.436914] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/motor/motor_controller_can.hpp\n'}
[0.439207] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/include/slpmu/slpmu_motor.hpp\n'}
[0.439322] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets.cmake\n'}
[0.439431] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/cmake/slpmu_motor_targets-release.cmake\n'}
[0.439524] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/slpmu_motor-config.cmake\n'}
[0.439616] (slpmu_motor) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin/motor_utils\n'}
[0.449890] (slpmu_power) StdoutLine: {'line': b'[100%] Built target power_utils\n'}
[0.451304] (slpmu_jack) JobEnded: {'identifier': 'slpmu_jack', 'rc': 0}
[0.454591] (slpmu_motor) CommandEnded: {'returncode': 0}
[0.459725] (sl_vcu_all) StdoutLine: {'line': b'[ 83%] Built target teleop_key\n'}
[0.461408] (sl_vcu_all) StdoutLine: {'line': b'[ 84%] Built target imu_sensor_node\n'}
[0.469699] (slpmu_motor) JobEnded: {'identifier': 'slpmu_motor', 'rc': 0}
[0.470237] (slpmu_ros2) JobStarted: {'identifier': 'slpmu_ros2'}
[0.487519] (slpmu_power) CommandEnded: {'returncode': 0}
[0.487842] (slpmu_power) JobProgress: {'identifier': 'slpmu_power', 'progress': 'install'}
[0.487871] (slpmu_power) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.495501] (slpmu_ros2) JobProgress: {'identifier': 'slpmu_ros2', 'progress': 'cmake'}
[0.495559] (sl_vcu_all) StdoutLine: {'line': b'[ 85%] Built target battery_monitor_node\n'}
[0.500250] (slpmu_ros2) JobProgress: {'identifier': 'slpmu_ros2', 'progress': 'build'}
[0.500309] (slpmu_ros2) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', '--', '-j4', '-l4'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.502631] (-) TimerEvent: {}
[0.506717] (slpmu_power) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.507042] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/libslpmu_power.a\n'}
[0.507196] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include\n'}
[0.507375] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu\n'}
[0.507477] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/slpmu_power.hpp\n'}
[0.507564] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power\n'}
[0.507665] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/i_battery.hpp\n'}
[0.507753] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/include/slpmu/power/battery_can.hpp\n'}
[0.507843] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets.cmake\n'}
[0.507939] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/cmake/slpmu_power_targets-noconfig.cmake\n'}
[0.508029] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/slpmu_power-config.cmake\n'}
[0.509404] (sl_vcu_all) StdoutLine: {'line': b'[ 86%] Built target led_display_control_node\n'}
[0.510012] (slpmu_power) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin/power_utils\n'}
[0.512106] (slpmu_power) CommandEnded: {'returncode': 0}
[0.512964] (sl_vcu_all) StdoutLine: {'line': b'running egg_info\n'}
[0.524387] (sl_vcu_all) StdoutLine: {'line': b'[ 86%] Built target sl_vcu_all\n'}
[0.536139] (slpmu_power) JobEnded: {'identifier': 'slpmu_power', 'rc': 0}
[0.539285] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target jack_control_node\n'}
[0.545010] (sl_vcu_all) StdoutLine: {'line': b'writing sl_vcu_all.egg-info/PKG-INFO\n'}
[0.545253] (sl_vcu_all) StdoutLine: {'line': b'writing dependency_links to sl_vcu_all.egg-info/dependency_links.txt\n'}
[0.545497] (sl_vcu_all) StdoutLine: {'line': b'writing top-level names to sl_vcu_all.egg-info/top_level.txt\n'}
[0.553529] (sl_vcu_all) StdoutLine: {'line': b'[ 88%] Built target sl_vcu_all__py\n'}
[0.560531] (slpmu_ros2) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[0.591464] (sl_vcu_all) StdoutLine: {'line': b'[ 96%] Built target sl_vcu_all__rosidl_generator_py\n'}
[0.600081] (slpmu_ros2) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter \n'}
[0.604432] (-) TimerEvent: {}
[0.626341] (sl_vcu_all) StdoutLine: {'line': b'[ 97%] Built target sl_vcu_all_s__rosidl_typesupport_introspection_c\n'}
[0.639449] (sl_vcu_all) StdoutLine: {'line': b"reading manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[0.640457] (sl_vcu_all) StdoutLine: {'line': b"writing manifest file 'sl_vcu_all.egg-info/SOURCES.txt'\n"}
[0.656786] (sl_vcu_all) StdoutLine: {'line': b'[ 99%] Built target sl_vcu_all_s__rosidl_typesupport_fastrtps_c\n'}
[0.678951] (sl_vcu_all) StdoutLine: {'line': b'[ 99%] Built target ament_cmake_python_build_sl_vcu_all_egg\n'}
[0.681837] (sl_vcu_all) StdoutLine: {'line': b'[100%] Built target sl_vcu_all_s__rosidl_typesupport_c\n'}
[0.700708] (sl_vcu_all) CommandEnded: {'returncode': 0}
[0.701759] (sl_vcu_all) JobProgress: {'identifier': 'sl_vcu_all', 'progress': 'install'}
[0.702360] (sl_vcu_all) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.704507] (-) TimerEvent: {}
[0.708205] (slpmu_ros2) StdoutLine: {'line': b'-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)\n'}
[0.730269] (sl_vcu_all) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.730779] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/rosidl_interfaces/sl_vcu_all\n'}
[0.731337] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.json\n'}
[0.731772] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.json\n'}
[0.732294] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.json\n'}
[0.732884] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.json\n'}
[0.733294] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.json\n'}
[0.752057] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.json\n'}
[0.753202] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.json\n'}
[0.754174] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.json\n'}
[0.758934] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.json\n'}
[0.760947] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.json\n'}
[0.761383] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.json\n'}
[0.761810] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.json\n'}
[0.762219] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.762636] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.763042] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.763453] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.h\n'}
[0.763861] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.h\n'}
[0.764264] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.h\n'}
[0.764675] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__functions.c\n'}
[0.765079] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[0.765511] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__description.c\n'}
[0.765898] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.h\n'}
[0.766280] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.766671] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.h\n'}
[0.767051] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.767436] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.h\n'}
[0.767819] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.h\n'}
[0.768202] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__functions.c\n'}
[0.768589] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.h\n'}
[0.768972] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[0.769353] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[0.769743] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.h\n'}
[0.770129] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__description.c\n'}
[0.770516] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.h\n'}
[0.770898] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__functions.c\n'}
[0.771333] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[0.771722] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.c\n'}
[0.772115] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__description.c\n'}
[0.772505] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__description.c\n'}
[0.772889] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.h\n'}
[0.773270] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.h\n'}
[0.773659] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__functions.h\n'}
[0.774046] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.h\n'}
[0.774429] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.h\n'}
[0.774811] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.h\n'}
[0.775192] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.775578] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.h\n'}
[0.775958] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.h\n'}
[0.776341] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.h\n'}
[0.776725] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.h\n'}
[0.777122] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_c__visibility_control.h\n'}
[0.777507] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.h\n'}
[0.777888] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.778269] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.h\n'}
[0.778655] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__description.c\n'}
[0.779038] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[0.779424] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__description.c\n'}
[0.779803] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__functions.c\n'}
[0.780183] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.c\n'}
[0.780569] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.h\n'}
[0.780953] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[0.781343] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.h\n'}
[0.781736] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[0.782120] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__functions.h\n'}
[0.782526] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.h\n'}
[0.782910] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.h\n'}
[0.783301] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.c\n'}
[0.783694] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.h\n'}
[0.784135] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[0.784531] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[0.784914] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__functions.h\n'}
[0.785296] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[0.785684] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__description.c\n'}
[0.786064] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__description.c\n'}
[0.786452] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.h\n'}
[0.786834] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.h\n'}
[0.787216] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.h\n'}
[0.787603] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__functions.c\n'}
[0.787983] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.h\n'}
[0.788379] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.h\n'}
[0.788769] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.h\n'}
[0.789153] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.h\n'}
[0.789538] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[0.789920] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[0.790302] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__description.c\n'}
[0.790693] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.h\n'}
[0.791081] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.c\n'}
[0.791467] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.h\n'}
[0.791854] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__functions.c\n'}
[0.792236] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__description.c\n'}
[0.792624] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.h\n'}
[0.793007] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__description.c\n'}
[0.793388] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__functions.c\n'}
[0.793785] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__description.c\n'}
[0.794168] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__functions.h\n'}
[0.794555] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.h\n'}
[0.794938] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.h\n'}
[0.795319] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.h\n'}
[0.795707] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__functions.c\n'}
[0.796087] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.h\n'}
[0.796473] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.h\n'}
[0.796857] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.h\n'}
[0.797239] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.h\n'}
[0.797627] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.h\n'}
[0.798007] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.sh\n'}
[0.798396] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/library_path.dsv\n'}
[0.798785] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_c.so\n'}
[0.799173] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.799579] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.799966] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.800418] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_c.h\n'}
[0.800808] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.801194] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.801584] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_c.h\n'}
[0.801966] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_c.h\n'}
[0.802348] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.802735] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.803114] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.803502] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.803883] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_c.h\n'}
[0.804263] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.804651] (-) TimerEvent: {}
[0.805012] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_c.h\n'}
[0.805466] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_c.h\n'}
[0.805861] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_c.h\n'}
[0.806250] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_c.h\n'}
[0.806644] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_c.h\n'}
[0.807027] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[0.807412] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_c.so\n'}
[0.807793] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.808179] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.808565] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.808945] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__builder.hpp\n'}
[0.809325] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__traits.hpp\n'}
[0.809722] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.hpp\n'}
[0.810103] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__struct.hpp\n'}
[0.810492] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/jack_control.hpp\n'}
[0.810874] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.811252] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/check_node_status.hpp\n'}
[0.811638] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.812037] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.hpp\n'}
[0.812425] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__builder.hpp\n'}
[0.812809] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__builder.hpp\n'}
[0.813192] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.hpp\n'}
[0.813580] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__builder.hpp\n'}
[0.813963] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__struct.hpp\n'}
[0.814342] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__traits.hpp\n'}
[0.814726] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__traits.hpp\n'}
[0.815108] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.hpp\n'}
[0.815497] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__traits.hpp\n'}
[0.815889] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__struct.hpp\n'}
[0.816269] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__struct.hpp\n'}
[0.816660] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/add_can_filter.hpp\n'}
[0.817045] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/led_control.hpp\n'}
[0.817431] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.817812] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_state.hpp\n'}
[0.818194] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/jack_status.hpp\n'}
[0.818581] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/bumper_state.hpp\n'}
[0.819002] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/motor_info.hpp\n'}
[0.819400] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.819782] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__traits.hpp\n'}
[0.820166] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.hpp\n'}
[0.820554] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__builder.hpp\n'}
[0.821034] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__traits.hpp\n'}
[0.821427] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__traits.hpp\n'}
[0.821809] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.hpp\n'}
[0.822191] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__builder.hpp\n'}
[0.822578] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__traits.hpp\n'}
[0.822960] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__builder.hpp\n'}
[0.823343] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__struct.hpp\n'}
[0.823761] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__traits.hpp\n'}
[0.824143] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__traits.hpp\n'}
[0.824533] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__builder.hpp\n'}
[0.824916] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__builder.hpp\n'}
[0.825307] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__traits.hpp\n'}
[0.825697] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.hpp\n'}
[0.826081] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.hpp\n'}
[0.826471] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.hpp\n'}
[0.826853] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.hpp\n'}
[0.827240] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__struct.hpp\n'}
[0.827627] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__struct.hpp\n'}
[0.828007] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__builder.hpp\n'}
[0.828386] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__struct.hpp\n'}
[0.828773] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__traits.hpp\n'}
[0.829154] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__struct.hpp\n'}
[0.829543] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__struct.hpp\n'}
[0.829940] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.hpp\n'}
[0.830366] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__struct.hpp\n'}
[0.830762] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.hpp\n'}
[0.831156] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__builder.hpp\n'}
[0.831552] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__builder.hpp\n'}
[0.831939] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__struct.hpp\n'}
[0.832322] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/can_frame.hpp\n'}
[0.832710] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/channel_data.hpp\n'}
[0.833090] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/part_data.hpp\n'}
[0.833489] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[0.833881] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/battery_status.hpp\n'}
[0.834269] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.834661] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.835049] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.835442] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/dds_fastrtps\n'}
[0.835845] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.836230] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.836618] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.837005] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/dds_fastrtps\n'}
[0.837396] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.837779] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.838174] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.838564] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.838948] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.839331] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/dds_fastrtps\n'}
[0.839724] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.840107] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.840497] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.840880] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.841261] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.841651] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.842266] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.842673] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[0.843039] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[0.843430] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_fastrtps_cpp.so\n'}
[0.843868] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.843968] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.844058] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.844142] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_c.h\n'}
[0.844226] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.c\n'}
[0.844321] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.846714] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.846809] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_c.h\n'}
[0.846914] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.c\n'}
[0.846999] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.c\n'}
[0.847111] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_c.h\n'}
[0.847197] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.c\n'}
[0.847286] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_c.h\n'}
[0.847377] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.847467] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[0.847548] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.847674] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.c\n'}
[0.847767] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_c.h\n'}
[0.847853] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_c.h\n'}
[0.847942] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.c\n'}
[0.848024] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.c\n'}
[0.848107] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_c.h\n'}
[0.848232] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_c.h\n'}
[0.848315] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.c\n'}
[0.848412] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.c\n'}
[0.848496] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.c\n'}
[0.848579] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.c\n'}
[0.848696] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.c\n'}
[0.848780] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_c.h\n'}
[0.848867] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_c.h\n'}
[0.848953] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_c.h\n'}
[0.849036] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_c.h\n'}
[0.849123] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_c.so\n'}
[0.849240] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_c.so\n'}
[0.849326] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all\n'}
[0.849420] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action\n'}
[0.849516] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail\n'}
[0.849627] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.849721] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/action/detail/jack_control__type_support.cpp\n'}
[0.849809] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv\n'}
[0.849899] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail\n'}
[0.849981] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__type_support.cpp\n'}
[0.850062] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__type_support.cpp\n'}
[0.852283] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/add_can_filter__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.852414] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__type_support.cpp\n'}
[0.852509] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/check_node_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.852601] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/srv/detail/led_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.852728] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg\n'}
[0.852841] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail\n'}
[0.852954] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__type_support.cpp\n'}
[0.853069] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.853186] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__type_support.cpp\n'}
[0.853305] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__type_support.cpp\n'}
[0.853428] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__type_support.cpp\n'}
[0.853529] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/jack_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.853667] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.853779] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/bumper_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.853894] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/battery_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.854015] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.854149] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/part_data__type_support.cpp\n'}
[0.854237] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_state__type_support.cpp\n'}
[0.854347] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/motor_info__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.854461] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/can_frame__type_support.cpp\n'}
[0.854567] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__rosidl_typesupport_introspection_cpp.hpp\n'}
[0.854686] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/include/sl_vcu_all/sl_vcu_all/msg/detail/channel_data__type_support.cpp\n'}
[0.854824] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_introspection_cpp.so\n'}
[0.854961] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_typesupport_cpp.so\n'}
[0.855083] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.sh\n'}
[0.855218] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/pythonpath.dsv\n'}
[0.855346] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info\n'}
[0.857558] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/SOURCES.txt\n'}
[0.858113] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/PKG-INFO\n'}
[0.858961] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/top_level.txt\n'}
[0.859154] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all-0.0.1-py3.12.egg-info/dependency_links.txt\n'}
[0.859297] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all\n'}
[0.859564] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_introspection_c.c\n'}
[0.859709] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[0.859890] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_c.c\n'}
[0.860059] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action\n'}
[0.860187] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/__init__.py\n'}
[0.860329] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control.py\n'}
[0.860514] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action/_jack_control_s.c\n'}
[0.860794] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/_sl_vcu_all_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[0.860934] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/__init__.py\n'}
[0.861117] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv\n'}
[0.861201] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status_s.c\n'}
[0.861283] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/__init__.py\n'}
[0.861365] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control.py\n'}
[0.864507] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_led_control_s.c\n'}
[0.864616] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_check_node_status.py\n'}
[0.864711] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter.py\n'}
[0.864797] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv/_add_can_filter_s.c\n'}
[0.864888] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[0.864994] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg\n'}
[0.865081] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info.py\n'}
[0.865166] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame_s.c\n'}
[0.865252] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data_s.c\n'}
[0.865341] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_info_s.c\n'}
[0.865432] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_part_data.py\n'}
[0.865520] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status_s.c\n'}
[0.865607] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status.py\n'}
[0.865699] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state_s.c\n'}
[0.865787] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/__init__.py\n'}
[0.865873] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_jack_status_s.c\n'}
[0.865956] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_bumper_state.py\n'}
[0.866043] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_battery_status.py\n'}
[0.866128] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_can_frame.py\n'}
[0.866216] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state.py\n'}
[0.866306] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data.py\n'}
[0.866401] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_motor_state_s.c\n'}
[0.866489] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg/_channel_data_s.c\n'}
[0.866576] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[0.866662] (slpmu_ros2) StdoutLine: {'line': b'-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)\n'}
[0.866755] (slpmu_ros2) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)\n'}
[0.866838] (slpmu_ros2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.866928] (slpmu_ros2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.867011] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all'...\n"}
[0.867099] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/action'...\n"}
[0.867188] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/msg'...\n"}
[0.867275] (sl_vcu_all) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/srv'...\n"}
[0.867359] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_fastrtps_c.so\n'}
[0.870587] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_introspection_c.so\n'}
[0.870700] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages/sl_vcu_all/sl_vcu_all_s__rosidl_typesupport_c.so\n'}
[0.870796] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/libsl_vcu_all__rosidl_generator_py.so\n'}
[0.870894] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.idl\n'}
[0.870991] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.idl\n'}
[0.871080] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.idl\n'}
[0.871170] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.idl\n'}
[0.871271] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.idl\n'}
[0.871362] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.idl\n'}
[0.871460] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.idl\n'}
[0.871541] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.idl\n'}
[0.871621] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.idl\n'}
[0.871708] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.idl\n'}
[0.871793] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.idl\n'}
[0.871882] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.idl\n'}
[0.871968] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/AddCanFilter.srv\n'}
[0.872055] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/CheckNodeStatus.srv\n'}
[0.872143] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/srv/LedControl.srv\n'}
[0.872230] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorInfo.msg\n'}
[0.872318] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/MotorState.msg\n'}
[0.872412] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BumperState.msg\n'}
[0.872502] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/CanFrame.msg\n'}
[0.872590] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/BatteryStatus.msg\n'}
[0.872680] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/PartData.msg\n'}
[0.872768] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/ChannelData.msg\n'}
[0.872854] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/msg/JackStatus.msg\n'}
[0.872940] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/action/JackControl.action\n'}
[0.873025] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/can_frame_dispatcher_node\n'}
[0.873118] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_controller_node\n'}
[0.873209] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/zl_motor_modbus_controller_node\n'}
[0.873296] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/bumper_sensor_node\n'}
[0.873384] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/imu_sensor_node\n'}
[0.876574] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/teleop_key\n'}
[0.876676] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/battery_monitor_node\n'}
[0.876762] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/jack_control_node\n'}
[0.876853] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/sl_vcu_all/led_display_control_node\n'}
[0.876945] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch\n'}
[0.877027] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/battery_monitor.launch.py\n'}
[0.877117] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/jack_control.launch.py\n'}
[0.877199] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/robot_localization_ekf.launch.py\n'}
[0.877281] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_controller.launch.py\n'}
[0.877363] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/zl_motor_modbus_controller.launch.py\n'}
[0.877457] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/led_control.launch.py\n'}
[0.877559] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/vcu_nodes.launch.py\n'}
[0.877646] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/imu_sensor.launch.py\n'}
[0.877733] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/bumper_sensor.launch.py\n'}
[0.877819] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/launch/sl_vcu_all.launch.py\n'}
[0.877905] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config\n'}
[0.877991] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/ekf.yaml\n'}
[0.878077] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_modbus_controller.yaml\n'}
[0.878166] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/battery_monitor.yaml\n'}
[0.878252] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/zl_motor_controller.yaml\n'}
[0.878340] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/jack_control.yaml\n'}
[0.878439] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/imu_sensor.yaml\n'}
[0.878528] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/robot_localization_ekf.yaml\n'}
[0.878615] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/bumper_sensor.yaml\n'}
[0.878701] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/config/led_control.yaml\n'}
[0.878788] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/package_run_dependencies/sl_vcu_all\n'}
[0.878875] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/parent_prefix_path/sl_vcu_all\n'}
[0.878963] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.sh\n'}
[0.879051] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/ament_prefix_path.dsv\n'}
[0.879138] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.sh\n'}
[0.879221] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/environment/path.dsv\n'}
[0.879308] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.bash\n'}
[0.879422] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.sh\n'}
[0.882534] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.zsh\n'}
[0.882630] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/local_setup.dsv\n'}
[0.882716] (sl_vcu_all) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv\n'}
[0.882800] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/ament_index/resource_index/packages/sl_vcu_all\n'}
[0.882892] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport.cmake\n'}
[0.882992] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cExport-noconfig.cmake\n'}
[0.883084] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[0.883174] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[0.883261] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_cppExport.cmake\n'}
[0.883351] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[0.883444] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[0.883553] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport.cmake\n'}
[0.883643] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[0.883730] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport.cmake\n'}
[0.883821] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cExport-noconfig.cmake\n'}
[0.883909] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport.cmake\n'}
[0.883999] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[0.884087] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport.cmake\n'}
[0.884176] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_all__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[0.884259] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport.cmake\n'}
[0.884347] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/export_sl_vcu_all__rosidl_generator_pyExport-noconfig.cmake\n'}
[0.884435] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake-extras.cmake\n'}
[0.884526] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.884615] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[0.884701] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[0.884791] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/ament_cmake_export_targets-extras.cmake\n'}
[0.884877] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[0.884963] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[0.885047] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig.cmake\n'}
[0.885135] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/cmake/sl_vcu_allConfig-version.cmake\n'}
[0.885222] (sl_vcu_all) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.xml\n'}
[0.885322] (sl_vcu_all) CommandEnded: {'returncode': 0}
[0.894855] (sl_vcu_all) JobEnded: {'identifier': 'sl_vcu_all', 'rc': 0}
[0.905446] (-) TimerEvent: {}
[0.905639] (slpmu_ros2) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)\n'}
[0.907558] (slpmu_ros2) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)\n'}
[0.995218] (slpmu_ros2) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.005538] (-) TimerEvent: {}
[1.084590] (slpmu_ros2) StdoutLine: {'line': b'-- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)\n'}
[1.103458] (slpmu_ros2) StdoutLine: {'line': b'-- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)\n'}
[1.105625] (-) TimerEvent: {}
[1.124377] (slpmu_ros2) StdoutLine: {'line': b'-- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)\n'}
[1.160514] (slpmu_ros2) StdoutLine: {'line': b'-- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)\n'}
[1.165455] (slpmu_ros2) StdoutLine: {'line': b'-- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)\n'}
[1.205741] (-) TimerEvent: {}
[1.306221] (-) TimerEvent: {}
[1.314853] (slpmu_ros2) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)\n'}
[1.319412] (slpmu_ros2) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)\n'}
[1.321112] (slpmu_ros2) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[1.323581] (slpmu_ros2) StdoutLine: {'line': b'-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)\n'}
[1.356609] (slpmu_ros2) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.6.0 (/opt/ros/jazzy/share/rosidl_default_generators/cmake)\n'}
[1.367470] (slpmu_ros2) StdoutLine: {'line': b'-- Found rosidl_adapter: 4.6.5 (/opt/ros/jazzy/share/rosidl_adapter/cmake)\n'}
[1.372819] (slpmu_ros2) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)\n'}
[1.406304] (-) TimerEvent: {}
[1.512044] (-) TimerEvent: {}
[1.612327] (-) TimerEvent: {}
[1.712604] (-) TimerEvent: {}
[1.747883] (slpmu_ros2) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.12.0 (/opt/ros/jazzy/share/ament_cmake_ros/cmake)\n'}
[1.812713] (-) TimerEvent: {}
[1.912976] (-) TimerEvent: {}
[2.013242] (-) TimerEvent: {}
[2.040757] (slpmu_ros2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[2.113366] (-) TimerEvent: {}
[2.154886] (slpmu_ros2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[2.156359] (slpmu_ros2) StdoutLine: {'line': b'-- Found python_cmake_module: 0.11.1 (/opt/ros/jazzy/share/python_cmake_module/cmake)\n'}
[2.213483] (-) TimerEvent: {}
[2.313744] (-) TimerEvent: {}
[2.414234] (-) TimerEvent: {}
[2.484386] (slpmu_ros2) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed \n'}
[2.514360] (-) TimerEvent: {}
[2.527416] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[2.528290] (slpmu_ros2) StdoutLine: {'line': b'-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include>\n'}
[2.528445] (slpmu_ros2) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[2.529053] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[2.529220] (slpmu_ros2) StdoutLine: {'line': b"-- Configured 'flake8' exclude dirs and/or files: \n"}
[2.529970] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[2.530660] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[2.532760] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[2.532931] (slpmu_ros2) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[2.533434] (slpmu_ros2) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[2.538383] (slpmu_ros2) StdoutLine: {'line': b'-- Configuring done (2.0s)\n'}
[2.614439] (-) TimerEvent: {}
[2.615631] (slpmu_ros2) StdoutLine: {'line': b'-- Generating done (0.1s)\n'}
[2.636082] (slpmu_ros2) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2\n'}
[2.688530] (slpmu_ros2) StdoutLine: {'line': b'[  1%] Built target slpmu_ros2__rosidl_generator_type_description\n'}
[2.701869] (slpmu_ros2) StdoutLine: {'line': b'[  1%] Built target ament_cmake_python_copy_slpmu_ros2\n'}
[2.714991] (-) TimerEvent: {}
[2.720416] (slpmu_ros2) StdoutLine: {'line': b'[  3%] Built target slpmu_ros2__cpp\n'}
[2.738495] (slpmu_ros2) StdoutLine: {'line': b'[  5%] \x1b[32m\x1b[1mLinking C shared library libslpmu_ros2__rosidl_generator_c.so\x1b[0m\n'}
[2.804702] (slpmu_ros2) StdoutLine: {'line': b'[ 22%] Built target slpmu_ros2__rosidl_generator_c\n'}
[2.815077] (-) TimerEvent: {}
[2.864440] (slpmu_ros2) StdoutLine: {'line': b'[ 23%] \x1b[32m\x1b[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_cpp.so\x1b[0m\n'}
[2.963474] (-) TimerEvent: {}
[3.054081] (slpmu_ros2) StdoutLine: {'line': b'[ 25%] \x1b[32m\x1b[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[3.063573] (-) TimerEvent: {}
[3.163846] (-) TimerEvent: {}
[3.265844] (-) TimerEvent: {}
[3.320424] (slpmu_ros2) StdoutLine: {'line': b'[ 32%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c\n'}
[3.333163] (slpmu_ros2) StdoutLine: {'line': b'[ 33%] \x1b[32m\x1b[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[3.346259] (slpmu_ros2) StdoutLine: {'line': b'[ 40%] Built target slpmu_ros2__rosidl_typesupport_cpp\n'}
[3.350427] (slpmu_ros2) StdoutLine: {'line': b'[ 42%] \x1b[32m\x1b[1mLinking C shared library libslpmu_ros2__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[3.365930] (-) TimerEvent: {}
[3.396963] (slpmu_ros2) StdoutLine: {'line': b'[ 44%] \x1b[32m\x1b[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_c.so\x1b[0m\n'}
[3.406825] (slpmu_ros2) StdoutLine: {'line': b'[ 50%] Built target slpmu_ros2__rosidl_typesupport_introspection_c\n'}
[3.458471] (slpmu_ros2) StdoutLine: {'line': b'[ 52%] \x1b[32m\x1b[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[3.468441] (-) TimerEvent: {}
[3.518908] (slpmu_ros2) StdoutLine: {'line': b'running egg_info\n'}
[3.531849] (slpmu_ros2) StdoutLine: {'line': b'[ 59%] Built target slpmu_ros2__rosidl_typesupport_c\n'}
[3.551754] (slpmu_ros2) StdoutLine: {'line': b'writing slpmu_ros2.egg-info/PKG-INFO\n'}
[3.552010] (slpmu_ros2) StdoutLine: {'line': b'writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt\n'}
[3.552218] (slpmu_ros2) StdoutLine: {'line': b'writing top-level names to slpmu_ros2.egg-info/top_level.txt\n'}
[3.568603] (-) TimerEvent: {}
[3.599135] (slpmu_ros2) StdoutLine: {'line': b'[ 66%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp\n'}
[3.617495] (slpmu_ros2) StdoutLine: {'line': b'[ 72%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp\n'}
[3.687805] (slpmu_ros2) StdoutLine: {'line': b"reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'\n"}
[3.688008] (-) TimerEvent: {}
[3.689435] (slpmu_ros2) StdoutLine: {'line': b"writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'\n"}
[3.709999] (slpmu_ros2) StdoutLine: {'line': b'[ 72%] Built target slpmu_ros2\n'}
[3.723087] (slpmu_ros2) StdoutLine: {'line': b'[ 72%] Built target ament_cmake_python_build_slpmu_ros2_egg\n'}
[3.733130] (slpmu_ros2) StdoutLine: {'line': b'[ 74%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o\x1b[0m\n'}
[3.739524] (slpmu_ros2) StdoutLine: {'line': b'[ 76%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o\x1b[0m\n'}
[3.759449] (slpmu_ros2) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o\x1b[0m\n'}
[3.788111] (-) TimerEvent: {}
[3.854480] (slpmu_ros2) StdoutLine: {'line': b'[ 79%] Built target slpmu_ros2__py\n'}
[3.888205] (-) TimerEvent: {}
[3.924190] (slpmu_ros2) StdoutLine: {'line': b'[ 81%] \x1b[32m\x1b[1mLinking C shared library libslpmu_ros2__rosidl_generator_py.so\x1b[0m\n'}
[3.988306] (-) TimerEvent: {}
[4.074226] (slpmu_ros2) StdoutLine: {'line': b'[ 86%] Built target slpmu_ros2__rosidl_generator_py\n'}
[4.088518] (-) TimerEvent: {}
[4.135363] (slpmu_ros2) StdoutLine: {'line': b'[ 88%] \x1b[32m\x1b[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[4.188610] (-) TimerEvent: {}
[4.289442] (-) TimerEvent: {}
[4.291549] (slpmu_ros2) StdoutLine: {'line': b'[ 89%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c\n'}
[4.316481] (slpmu_ros2) StdoutLine: {'line': b'[ 91%] \x1b[32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/jack_operator.cpp.o\x1b[0m\n'}
[4.389545] (-) TimerEvent: {}
[4.489808] (-) TimerEvent: {}
[4.596454] (-) TimerEvent: {}
[4.697047] (-) TimerEvent: {}
[4.797578] (-) TimerEvent: {}
[4.840971] (slpmu_ros2) StdoutLine: {'line': b'[ 93%] \x1b[32m\x1b[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[4.897673] (-) TimerEvent: {}
[5.000472] (-) TimerEvent: {}
[5.058981] (slpmu_ros2) StdoutLine: {'line': b'[ 94%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c\n'}
[5.100569] (-) TimerEvent: {}
[5.152387] (slpmu_ros2) StdoutLine: {'line': b'[ 96%] \x1b[32m\x1b[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so\x1b[0m\n'}
[5.200666] (-) TimerEvent: {}
[5.278505] (slpmu_ros2) StdoutLine: {'line': b'[ 98%] Built target slpmu_ros2_s__rosidl_typesupport_c\n'}
[5.300761] (-) TimerEvent: {}
[5.401313] (-) TimerEvent: {}
[5.501881] (-) TimerEvent: {}
[5.602730] (-) TimerEvent: {}
[5.704666] (-) TimerEvent: {}
[5.805037] (-) TimerEvent: {}
[5.905422] (-) TimerEvent: {}
[6.005804] (-) TimerEvent: {}
[6.106555] (-) TimerEvent: {}
[6.207213] (-) TimerEvent: {}
[6.308476] (-) TimerEvent: {}
[6.408874] (-) TimerEvent: {}
[6.509263] (-) TimerEvent: {}
[6.609662] (-) TimerEvent: {}
[6.710472] (-) TimerEvent: {}
[6.810848] (-) TimerEvent: {}
[6.911468] (-) TimerEvent: {}
[7.012474] (-) TimerEvent: {}
[7.112843] (-) TimerEvent: {}
[7.213217] (-) TimerEvent: {}
[7.314474] (-) TimerEvent: {}
[7.414850] (-) TimerEvent: {}
[7.515231] (-) TimerEvent: {}
[7.615623] (-) TimerEvent: {}
[7.716472] (-) TimerEvent: {}
[7.816849] (-) TimerEvent: {}
[7.917185] (-) TimerEvent: {}
[8.019713] (-) TimerEvent: {}
[8.120218] (-) TimerEvent: {}
[8.220720] (-) TimerEvent: {}
[8.321213] (-) TimerEvent: {}
[8.422449] (-) TimerEvent: {}
[8.522832] (-) TimerEvent: {}
[8.623527] (-) TimerEvent: {}
[8.724167] (-) TimerEvent: {}
[8.825146] (-) TimerEvent: {}
[8.925546] (-) TimerEvent: {}
[9.025833] (-) TimerEvent: {}
[9.126126] (-) TimerEvent: {}
[9.226606] (-) TimerEvent: {}
[9.327101] (-) TimerEvent: {}
[9.427419] (-) TimerEvent: {}
[9.527719] (-) TimerEvent: {}
[9.628035] (-) TimerEvent: {}
[9.728325] (-) TimerEvent: {}
[9.828757] (-) TimerEvent: {}
[9.929038] (-) TimerEvent: {}
[10.029321] (-) TimerEvent: {}
[10.129599] (-) TimerEvent: {}
[10.229882] (-) TimerEvent: {}
[10.330183] (-) TimerEvent: {}
[10.430495] (-) TimerEvent: {}
[10.530781] (-) TimerEvent: {}
[10.631077] (-) TimerEvent: {}
[10.731370] (-) TimerEvent: {}
[10.831771] (-) TimerEvent: {}
[10.932056] (-) TimerEvent: {}
[11.032345] (-) TimerEvent: {}
[11.133438] (-) TimerEvent: {}
[11.233704] (-) TimerEvent: {}
[11.334143] (-) TimerEvent: {}
[11.434443] (-) TimerEvent: {}
[11.534718] (-) TimerEvent: {}
[11.635028] (-) TimerEvent: {}
[11.735328] (-) TimerEvent: {}
[11.835643] (-) TimerEvent: {}
[11.935899] (-) TimerEvent: {}
[12.036214] (-) TimerEvent: {}
[12.136483] (-) TimerEvent: {}
[12.236785] (-) TimerEvent: {}
[12.337069] (-) TimerEvent: {}
[12.437339] (-) TimerEvent: {}
[12.537598] (-) TimerEvent: {}
[12.637890] (-) TimerEvent: {}
[12.738155] (-) TimerEvent: {}
[12.838593] (-) TimerEvent: {}
[12.938883] (-) TimerEvent: {}
[13.039204] (-) TimerEvent: {}
[13.139496] (-) TimerEvent: {}
[13.239792] (-) TimerEvent: {}
[13.340063] (-) TimerEvent: {}
[13.440467] (-) TimerEvent: {}
[13.540739] (-) TimerEvent: {}
[13.641050] (-) TimerEvent: {}
[13.741360] (-) TimerEvent: {}
[13.841662] (-) TimerEvent: {}
[13.941932] (-) TimerEvent: {}
[14.042448] (-) TimerEvent: {}
[14.142989] (-) TimerEvent: {}
[14.243428] (-) TimerEvent: {}
[14.343713] (-) TimerEvent: {}
[14.443996] (-) TimerEvent: {}
[14.544476] (-) TimerEvent: {}
[14.644777] (-) TimerEvent: {}
[14.745073] (-) TimerEvent: {}
[14.845369] (-) TimerEvent: {}
[14.945630] (-) TimerEvent: {}
[15.045932] (-) TimerEvent: {}
[15.146230] (-) TimerEvent: {}
[15.246491] (-) TimerEvent: {}
[15.346805] (-) TimerEvent: {}
[15.447104] (-) TimerEvent: {}
[15.547428] (-) TimerEvent: {}
[15.647775] (-) TimerEvent: {}
[15.748074] (-) TimerEvent: {}
[15.848433] (-) TimerEvent: {}
[15.948736] (-) TimerEvent: {}
[16.049035] (-) TimerEvent: {}
[16.149299] (-) TimerEvent: {}
[16.249626] (-) TimerEvent: {}
[16.349939] (-) TimerEvent: {}
[16.450226] (-) TimerEvent: {}
[16.550510] (-) TimerEvent: {}
[16.650801] (-) TimerEvent: {}
[16.751117] (-) TimerEvent: {}
[16.851430] (-) TimerEvent: {}
[16.951754] (-) TimerEvent: {}
[17.052063] (-) TimerEvent: {}
[17.152380] (-) TimerEvent: {}
[17.252750] (-) TimerEvent: {}
[17.353062] (-) TimerEvent: {}
[17.453361] (-) TimerEvent: {}
[17.553664] (-) TimerEvent: {}
[17.653964] (-) TimerEvent: {}
[17.754263] (-) TimerEvent: {}
[17.854601] (-) TimerEvent: {}
[17.954912] (-) TimerEvent: {}
[18.055222] (-) TimerEvent: {}
[18.155506] (-) TimerEvent: {}
[18.255828] (-) TimerEvent: {}
[18.356122] (-) TimerEvent: {}
[18.456470] (-) TimerEvent: {}
[18.556770] (-) TimerEvent: {}
[18.657110] (-) TimerEvent: {}
[18.757453] (-) TimerEvent: {}
[18.857781] (-) TimerEvent: {}
[18.958076] (-) TimerEvent: {}
[19.058373] (-) TimerEvent: {}
[19.158696] (-) TimerEvent: {}
[19.258979] (-) TimerEvent: {}
[19.359272] (-) TimerEvent: {}
[19.459538] (-) TimerEvent: {}
[19.559828] (-) TimerEvent: {}
[19.660127] (-) TimerEvent: {}
[19.760484] (-) TimerEvent: {}
[19.860786] (-) TimerEvent: {}
[19.961228] (-) TimerEvent: {}
[20.061491] (-) TimerEvent: {}
[20.161783] (-) TimerEvent: {}
[20.262081] (-) TimerEvent: {}
[20.362401] (-) TimerEvent: {}
[20.462757] (-) TimerEvent: {}
[20.563218] (-) TimerEvent: {}
[20.663480] (-) TimerEvent: {}
[20.763805] (-) TimerEvent: {}
[20.864109] (-) TimerEvent: {}
[20.964436] (-) TimerEvent: {}
[21.064750] (-) TimerEvent: {}
[21.165039] (-) TimerEvent: {}
[21.265348] (-) TimerEvent: {}
[21.365679] (-) TimerEvent: {}
[21.465976] (-) TimerEvent: {}
[21.566273] (-) TimerEvent: {}
[21.666555] (-) TimerEvent: {}
[21.766856] (-) TimerEvent: {}
[21.867136] (-) TimerEvent: {}
[21.967483] (-) TimerEvent: {}
[22.067780] (-) TimerEvent: {}
[22.168072] (-) TimerEvent: {}
[22.268336] (-) TimerEvent: {}
[22.368679] (-) TimerEvent: {}
[22.469011] (-) TimerEvent: {}
[22.569372] (-) TimerEvent: {}
[22.669662] (-) TimerEvent: {}
[22.769964] (-) TimerEvent: {}
[22.870270] (-) TimerEvent: {}
[22.970573] (-) TimerEvent: {}
[23.070868] (-) TimerEvent: {}
[23.171158] (-) TimerEvent: {}
[23.271494] (-) TimerEvent: {}
[23.371779] (-) TimerEvent: {}
[23.472075] (-) TimerEvent: {}
[23.572371] (-) TimerEvent: {}
[23.672656] (-) TimerEvent: {}
[23.773007] (-) TimerEvent: {}
[23.873328] (-) TimerEvent: {}
[23.973714] (-) TimerEvent: {}
[24.074011] (-) TimerEvent: {}
[24.174312] (-) TimerEvent: {}
[24.274686] (-) TimerEvent: {}
[24.374982] (-) TimerEvent: {}
[24.475282] (-) TimerEvent: {}
[24.575586] (-) TimerEvent: {}
[24.675922] (-) TimerEvent: {}
[24.776281] (-) TimerEvent: {}
[24.876587] (-) TimerEvent: {}
[24.976882] (-) TimerEvent: {}
[25.077196] (-) TimerEvent: {}
[25.177488] (-) TimerEvent: {}
[25.277800] (-) TimerEvent: {}
[25.378112] (-) TimerEvent: {}
[25.478426] (-) TimerEvent: {}
[25.578761] (-) TimerEvent: {}
[25.679059] (-) TimerEvent: {}
[25.779361] (-) TimerEvent: {}
[25.879698] (-) TimerEvent: {}
[25.979999] (-) TimerEvent: {}
[26.080301] (-) TimerEvent: {}
[26.180618] (-) TimerEvent: {}
[26.280920] (-) TimerEvent: {}
[26.381216] (-) TimerEvent: {}
[26.481486] (-) TimerEvent: {}
[26.581763] (-) TimerEvent: {}
[26.682064] (-) TimerEvent: {}
[26.782373] (-) TimerEvent: {}
[26.882700] (-) TimerEvent: {}
[26.982998] (-) TimerEvent: {}
[27.083301] (-) TimerEvent: {}
[27.183602] (-) TimerEvent: {}
[27.283942] (-) TimerEvent: {}
[27.384241] (-) TimerEvent: {}
[27.484486] (-) TimerEvent: {}
[27.584782] (-) TimerEvent: {}
[27.685089] (-) TimerEvent: {}
[27.785421] (-) TimerEvent: {}
[27.885726] (-) TimerEvent: {}
[27.986024] (-) TimerEvent: {}
[28.086333] (-) TimerEvent: {}
[28.186683] (-) TimerEvent: {}
[28.286998] (-) TimerEvent: {}
[28.387297] (-) TimerEvent: {}
[28.487576] (-) TimerEvent: {}
[28.587882] (-) TimerEvent: {}
[28.688187] (-) TimerEvent: {}
[28.788488] (-) TimerEvent: {}
[28.888781] (-) TimerEvent: {}
[28.989138] (-) TimerEvent: {}
[29.089489] (-) TimerEvent: {}
[29.192102] (-) TimerEvent: {}
[29.292377] (-) TimerEvent: {}
[29.392706] (-) TimerEvent: {}
[29.493097] (-) TimerEvent: {}
[29.593427] (-) TimerEvent: {}
[29.693745] (-) TimerEvent: {}
[29.794046] (-) TimerEvent: {}
[29.894349] (-) TimerEvent: {}
[29.994669] (-) TimerEvent: {}
[30.095085] (-) TimerEvent: {}
[30.195419] (-) TimerEvent: {}
[30.295762] (-) TimerEvent: {}
[30.396063] (-) TimerEvent: {}
[30.496427] (-) TimerEvent: {}
[30.596749] (-) TimerEvent: {}
[30.697052] (-) TimerEvent: {}
[30.797352] (-) TimerEvent: {}
[30.897650] (-) TimerEvent: {}
[30.967582] (slpmu_ros2) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable slpmu_node\x1b[0m\n'}
[30.997768] (-) TimerEvent: {}
[31.098070] (-) TimerEvent: {}
[31.198379] (-) TimerEvent: {}
[31.245933] (slpmu_ros2) StdoutLine: {'line': b'[100%] Built target slpmu_node\n'}
[31.261697] (slpmu_ros2) CommandEnded: {'returncode': 0}
[31.262655] (slpmu_ros2) JobProgress: {'identifier': 'slpmu_ros2', 'progress': 'install'}
[31.263240] (slpmu_ros2) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2'], 'cwd': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_US:en', 'USER': 'yuan', 'SSH_CLIENT': '********** 8658 22', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'tty', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'OLDPWD': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu', 'SSH_TTY': '/dev/pts/0', 'ROS_PYTHON_VERSION': '3', 'PS1': '\\u@\\h \\[\\033[01;36m\\]\\w\\[\\033[01;32m\\]$(git_branch)\\[\\033[00m\\] \\$', 'LC_MONETARY': 'en_US.UTF-8', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'yuan', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'PKG_CONFIG_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig', 'TERM': 'xterm', 'XDG_SESSION_ID': '5288', 'PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin', 'PAPERSIZE': 'letter', 'LC_ADDRESS': 'en_US.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'DISPLAY': 'localhost:10.0', 'LANG': 'en_US.UTF-8', 'LC_TELEPHONE': 'en_US.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '0', 'AMENT_PREFIX_PATH': '/opt/ros/jazzy', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'LC_ALL': 'en_US.UTF-8', 'SSH_CONNECTION': '********** 8658 ************ 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[31.274349] (slpmu_ros2) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[31.274634] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/rosidl_interfaces/slpmu_ros2\n'}
[31.274886] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/action/JackControl.json\n'}
[31.275119] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/JackStatus.json\n'}
[31.275335] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/MotorState.json\n'}
[31.275447] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2\n'}
[31.275539] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action\n'}
[31.275649] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail\n'}
[31.275911] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__struct.h\n'}
[31.276035] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__functions.h\n'}
[31.276152] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.h\n'}
[31.276329] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__functions.c\n'}
[31.276502] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.c\n'}
[31.276618] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__description.c\n'}
[31.276708] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/jack_control.h\n'}
[31.276813] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg\n'}
[31.276905] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/motor_state.h\n'}
[31.276996] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_generator_c__visibility_control.h\n'}
[31.277083] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail\n'}
[31.277192] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.c\n'}
[31.277276] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__functions.h\n'}
[31.277362] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.c\n'}
[31.277459] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__functions.c\n'}
[31.277622] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__functions.h\n'}
[31.277795] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__description.c\n'}
[31.277929] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.h\n'}
[31.278019] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__functions.c\n'}
[31.278104] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__description.c\n'}
[31.278209] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__struct.h\n'}
[31.278300] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.h\n'}
[31.278387] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__struct.h\n'}
[31.278482] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/jack_status.h\n'}
[31.278565] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/library_path.sh\n'}
[31.278651] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/library_path.dsv\n'}
[31.278739] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_generator_c.so\n'}
[31.278876] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_generator_c.so" to ""\n'}
[31.279032] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2\n'}
[31.279152] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action\n'}
[31.279345] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail\n'}
[31.279514] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__rosidl_typesupport_fastrtps_c.h\n'}
[31.279600] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg\n'}
[31.279729] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail\n'}
[31.279887] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__rosidl_typesupport_fastrtps_c.h\n'}
[31.280026] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__rosidl_typesupport_fastrtps_c.h\n'}
[31.280157] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[31.280247] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_fastrtps_c.so\n'}
[31.280333] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[31.280447] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2\n'}
[31.280534] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action\n'}
[31.280623] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail\n'}
[31.280724] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__builder.hpp\n'}
[31.280808] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__traits.hpp\n'}
[31.280897] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.hpp\n'}
[31.280985] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__struct.hpp\n'}
[31.281107] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/jack_control.hpp\n'}
[31.281260] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg\n'}
[31.281379] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/motor_state.hpp\n'}
[31.281490] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/jack_status.hpp\n'}
[31.281581] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail\n'}
[31.281669] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__traits.hpp\n'}
[31.281759] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.hpp\n'}
[31.281845] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__traits.hpp\n'}
[31.281934] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__builder.hpp\n'}
[31.282024] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__struct.hpp\n'}
[31.282111] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.hpp\n'}
[31.282195] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__builder.hpp\n'}
[31.282283] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__struct.hpp\n'}
[31.282368] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[31.282488] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2\n'}
[31.282572] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action\n'}
[31.282661] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail\n'}
[31.282743] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/dds_fastrtps\n'}
[31.282832] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[31.282914] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg\n'}
[31.283005] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail\n'}
[31.283090] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/dds_fastrtps\n'}
[31.283179] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[31.283267] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[31.283354] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[31.283447] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_fastrtps_cpp.so\n'}
[31.283549] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_fastrtps_cpp.so" to ""\n'}
[31.283636] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2\n'}
[31.283726] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action\n'}
[31.283820] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail\n'}
[31.283909] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__rosidl_typesupport_introspection_c.h\n'}
[31.284159] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.c\n'}
[31.284301] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg\n'}
[31.284409] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[31.284502] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail\n'}
[31.284589] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.c\n'}
[31.284676] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.c\n'}
[31.284762] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__rosidl_typesupport_introspection_c.h\n'}
[31.284861] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__rosidl_typesupport_introspection_c.h\n'}
[31.284981] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_introspection_c.so\n'}
[31.285070] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_introspection_c.so" to ""\n'}
[31.285200] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_c.so\n'}
[31.285293] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_c.so" to ""\n'}
[31.285384] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2\n'}
[31.285482] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action\n'}
[31.285569] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail\n'}
[31.285657] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__rosidl_typesupport_introspection_cpp.hpp\n'}
[31.285744] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.cpp\n'}
[31.285913] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg\n'}
[31.286055] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail\n'}
[31.286155] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.cpp\n'}
[31.286275] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[31.286401] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[31.286534] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.cpp\n'}
[31.286626] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_introspection_cpp.so\n'}
[31.286710] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_introspection_cpp.so" to ""\n'}
[31.286854] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_cpp.so\n'}
[31.286939] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_cpp.so" to ""\n'}
[31.287034] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/pythonpath.sh\n'}
[31.287118] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/pythonpath.dsv\n'}
[31.287204] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info\n'}
[31.287340] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info/SOURCES.txt\n'}
[31.287454] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info/PKG-INFO\n'}
[31.287547] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info/top_level.txt\n'}
[31.287633] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info/dependency_links.txt\n'}
[31.287718] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2\n'}
[31.287802] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_c.c\n'}
[31.287890] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[31.287976] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action\n'}
[31.288062] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action/__init__.py\n'}
[31.288148] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action/_jack_control.py\n'}
[31.288233] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action/_jack_control_s.c\n'}
[31.288320] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/__init__.py\n'}
[31.288415] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so\n'}
[31.288511] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_introspection_c.c\n'}
[31.288594] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so\n'}
[31.288821] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg\n'}
[31.288930] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_jack_status.py\n'}
[31.289019] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/__init__.py\n'}
[31.289106] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_jack_status_s.c\n'}
[31.289189] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_motor_state.py\n'}
[31.289275] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_motor_state_s.c\n'}
[31.289364] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so\n'}
[31.298462] (-) TimerEvent: {}
[31.332303] (slpmu_ros2) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2'...\n"}
[31.332536] (slpmu_ros2) StdoutLine: {'line': b"Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/__init__.py'...\n"}
[31.332656] (slpmu_ros2) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action'...\n"}
[31.332757] (slpmu_ros2) StdoutLine: {'line': b"Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg'...\n"}
[31.339420] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so\n'}
[31.340000] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[31.340323] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so\n'}
[31.340854] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so" to ""\n'}
[31.341190] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so\n'}
[31.341790] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so" to ""\n'}
[31.342086] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_generator_py.so\n'}
[31.342745] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_generator_py.so" to ""\n'}
[31.343006] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/action/JackControl.idl\n'}
[31.343221] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/JackStatus.idl\n'}
[31.343349] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/MotorState.idl\n'}
[31.343470] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/action/JackControl.action\n'}
[31.343564] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/JackStatus.msg\n'}
[31.343710] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/MotorState.msg\n'}
[31.343822] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/slpmu_ros2/slpmu_node\n'}
[31.347149] (slpmu_ros2) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/slpmu_ros2/slpmu_node" to ""\n'}
[31.347406] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/launch\n'}
[31.347538] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/launch/slpmu_ros2.launch.py\n'}
[31.347703] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/config\n'}
[31.347853] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/config/slpmu_ros2.yaml\n'}
[31.347995] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/package_run_dependencies/slpmu_ros2\n'}
[31.348103] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/parent_prefix_path/slpmu_ros2\n'}
[31.348209] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/ament_prefix_path.sh\n'}
[31.348301] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/ament_prefix_path.dsv\n'}
[31.348423] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/path.sh\n'}
[31.348537] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/path.dsv\n'}
[31.348645] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.bash\n'}
[31.348749] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.sh\n'}
[31.348883] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.zsh\n'}
[31.348995] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.dsv\n'}
[31.349118] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv\n'}
[31.349225] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/packages/slpmu_ros2\n'}
[31.349346] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_cExport.cmake\n'}
[31.349478] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_cExport-release.cmake\n'}
[31.349628] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[31.349760] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cExport-release.cmake\n'}
[31.349850] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_cppExport.cmake\n'}
[31.349979] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[31.350122] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cppExport-release.cmake\n'}
[31.350258] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cExport.cmake\n'}
[31.350363] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cExport-release.cmake\n'}
[31.350462] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cExport.cmake\n'}
[31.350550] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cExport-release.cmake\n'}
[31.350638] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cppExport.cmake\n'}
[31.350728] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cppExport-release.cmake\n'}
[31.350815] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cppExport.cmake\n'}
[31.350902] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cppExport-release.cmake\n'}
[31.350985] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_pyExport.cmake\n'}
[31.351070] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_pyExport-release.cmake\n'}
[31.351151] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/rosidl_cmake-extras.cmake\n'}
[31.351238] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[31.351329] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[31.351435] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[31.351515] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/ament_cmake_export_targets-extras.cmake\n'}
[31.351598] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[31.351683] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[31.351768] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2Config.cmake\n'}
[31.351851] (slpmu_ros2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2Config-version.cmake\n'}
[31.351946] (slpmu_ros2) StdoutLine: {'line': b'-- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.xml\n'}
[31.353292] (slpmu_ros2) CommandEnded: {'returncode': 0}
[31.376309] (slpmu_ros2) JobEnded: {'identifier': 'slpmu_ros2', 'rc': 0}
[31.377255] (-) EventReactorShutdown: {}
