[0.138s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.138s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x758c55949eb0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x758c55949b50>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x758c55949b50>>, mixin_verb=('build',))
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.186s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.186s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/works/source/amr_vcu_test_ws'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ignore', 'ignore_ament_install']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ros'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['cmake', 'python']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'cmake'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['python_setup_py']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python_setup_py'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_meta'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ros']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ros'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['cmake', 'python']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'cmake'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['python_setup_py']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python_setup_py'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_meta']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ros'
[0.226s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slcan' with type 'ros.cmake' and name 'slcan'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_pkg']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_pkg'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ros'
[0.227s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_jack' with type 'ros.cmake' and name 'slpmu_jack'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_pkg']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_pkg'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_meta']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ros'
[0.229s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_motor' with type 'ros.cmake' and name 'slpmu_motor'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore_ament_install'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_pkg']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_pkg'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_meta']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_meta'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ros']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ros'
[0.230s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_power' with type 'ros.cmake' and name 'slpmu_power'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore_ament_install'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_pkg']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ros'
[0.231s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_process' with type 'ros.cmake' and name 'slpmu_process'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_pkg'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_meta']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_meta'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ros']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ros'
[0.233s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_ros2' with type 'ros.ament_cmake' and name 'slpmu_ros2'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ros'
[0.235s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sl_vcu_all' with type 'ros.ament_cmake' and name 'sl_vcu_all'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['ignore', 'ignore_ament_install']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ignore'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ignore_ament_install'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['colcon_pkg']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'colcon_pkg'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['colcon_meta']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'colcon_meta'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['ros']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ros'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['cmake', 'python']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'cmake'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'python'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['python_setup_py']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'python_setup_py'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ignore'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ignore_ament_install'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['colcon_pkg']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'colcon_pkg'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['colcon_meta']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'colcon_meta'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ros'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['cmake', 'python']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'cmake'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'python'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['python_setup_py']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'python_setup_py'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ros'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['cmake', 'python']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'cmake'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'python'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['python_setup_py']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'python_setup_py'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['ignore', 'ignore_ament_install']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ignore'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ignore_ament_install'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['colcon_pkg']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'colcon_pkg'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['colcon_meta']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'colcon_meta'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['ros']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ros'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['cmake', 'python']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'cmake'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'python'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['python_setup_py']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'python_setup_py'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['ignore', 'ignore_ament_install']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ignore'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ignore_ament_install'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['colcon_pkg']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'colcon_pkg'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['colcon_meta']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'colcon_meta'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['ros']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ros'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['cmake', 'python']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'cmake'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'python'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['python_setup_py']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'python_setup_py'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['ignore', 'ignore_ament_install']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ignore'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ignore_ament_install'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['colcon_pkg']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'colcon_pkg'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['colcon_meta']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'colcon_meta'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['ros']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ros'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['cmake', 'python']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'cmake'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'python'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['python_setup_py']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'python_setup_py'
[0.241s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.241s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.241s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.241s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.241s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.267s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 393 installed packages in /opt/ros/jazzy
[0.268s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.338s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_args' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_cache' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_first' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_force_configure' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'ament_cmake_args' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_cmake_args' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.338s] DEBUG:colcon.colcon_core.verb:Building package 'sl_vcu_all' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all', 'symlink_install': False, 'test_result_base': None}
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_cache' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_first' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_force_configure' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'ament_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.339s] DEBUG:colcon.colcon_core.verb:Building package 'slcan' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan', 'symlink_install': False, 'test_result_base': None}
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_cache' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_first' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_force_configure' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'ament_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.339s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_process' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process', 'symlink_install': False, 'test_result_base': None}
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_cache' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_first' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_force_configure' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'ament_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.340s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_jack' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack', 'symlink_install': False, 'test_result_base': None}
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_cache' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_first' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_force_configure' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'ament_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.341s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_motor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor', 'symlink_install': False, 'test_result_base': None}
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_cache' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_first' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_force_configure' from command line to 'False'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'ament_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_cmake_args' from command line to 'None'
[0.341s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.341s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_power' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power', 'symlink_install': False, 'test_result_base': None}
[0.342s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_cache' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_first' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_force_configure' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'ament_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.342s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_ros2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2', 'symlink_install': False, 'test_result_base': None}
[0.342s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.343s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.343s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan' with build type 'cmake'
[0.344s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan'
[0.346s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.346s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.346s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.350s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process' with build type 'cmake'
[0.350s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process'
[0.351s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.351s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.355s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all' with build type 'ament_cmake'
[0.355s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all'
[0.355s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.355s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.364s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.366s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process -- -j4 -l4
[0.370s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[0.451s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.477s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.484s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process -- -j4 -l4
[0.484s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process
[0.510s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path')
[0.510s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.ps1'
[0.513s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.dsv'
[0.514s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.sh'
[0.518s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.521s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path_multiarch')
[0.521s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.ps1'
[0.523s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.dsv'
[0.524s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.sh'
[0.526s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slcan)
[0.531s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan' for CMake module files
[0.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan' for CMake config files
[0.532s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'cmake_prefix_path')
[0.532s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.ps1'
[0.533s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.dsv'
[0.534s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.sh'
[0.535s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib'
[0.535s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/bin'
[0.535s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig/slcan.pc'
[0.536s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/python3.12/site-packages'
[0.536s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/bin'
[0.536s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.ps1'
[0.538s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.dsv'
[0.538s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.sh'
[0.539s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.bash'
[0.540s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.zsh'
[0.544s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/colcon-core/packages/slcan)
[0.545s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack' with build type 'cmake'
[0.546s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack'
[0.546s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.546s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.550s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor' with build type 'cmake'
[0.551s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor'
[0.551s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.552s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.559s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process
[0.559s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path')
[0.559s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.ps1'
[0.560s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.dsv'
[0.561s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.sh'
[0.562s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path_multiarch')
[0.563s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.ps1'
[0.563s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.dsv'
[0.564s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.sh'
[0.564s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_process)
[0.564s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process' for CMake module files
[0.565s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process' for CMake config files
[0.565s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'cmake_prefix_path')
[0.566s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.ps1'
[0.567s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.dsv'
[0.567s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.sh'
[0.568s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib'
[0.568s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/bin'
[0.569s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig/slpmu_process.pc'
[0.569s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/python3.12/site-packages'
[0.569s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/bin'
[0.570s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.ps1'
[0.571s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.dsv'
[0.571s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.sh'
[0.572s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.bash'
[0.572s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.zsh'
[0.573s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/colcon-core/packages/slpmu_process)
[0.574s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power' with build type 'cmake'
[0.575s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power'
[0.575s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.575s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.585s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[0.592s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor -- -j4 -l4
[0.602s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power -- -j4 -l4
[0.841s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power -- -j4 -l4
[0.842s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power
[0.864s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[0.865s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[0.877s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path')
[0.877s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power
[0.878s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.ps1'
[0.878s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.dsv'
[0.879s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.sh'
[0.879s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path_multiarch')
[0.882s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.ps1'
[0.882s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.dsv'
[0.883s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.sh'
[0.884s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_power)
[0.887s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power' for CMake module files
[0.887s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power' for CMake config files
[0.888s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'cmake_prefix_path')
[0.888s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.ps1'
[0.889s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.dsv'
[0.889s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.sh'
[0.892s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib'
[0.892s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin'
[0.893s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'path')
[0.893s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.ps1'
[0.894s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.dsv'
[0.894s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.sh'
[0.895s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/pkgconfig/slpmu_power.pc'
[0.895s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/python3.12/site-packages'
[0.896s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin'
[0.897s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pythonscriptspath')
[0.897s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.ps1'
[0.900s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.dsv'
[0.902s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.sh'
[0.905s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.ps1'
[0.910s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.dsv'
[0.911s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.sh'
[0.915s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.bash'
[0.915s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.zsh'
[0.916s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/colcon-core/packages/slpmu_power)
[0.920s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor -- -j4 -l4
[0.921s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor
[0.922s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path')
[0.923s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.ps1'
[0.923s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[0.924s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.dsv'
[0.924s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.sh'
[0.925s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path_multiarch')
[0.926s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.ps1'
[0.926s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.dsv'
[0.927s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.sh'
[0.928s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_jack)
[0.928s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake module files
[0.929s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake config files
[0.929s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'cmake_prefix_path')
[0.929s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.ps1'
[0.930s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.dsv'
[0.931s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.sh'
[0.932s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib'
[0.932s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[0.932s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'path')
[0.932s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.ps1'
[0.933s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.dsv'
[0.934s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.sh'
[0.935s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig/slpmu_jack.pc'
[0.935s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/python3.12/site-packages'
[0.935s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[0.935s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pythonscriptspath')
[0.936s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.ps1'
[0.937s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.dsv'
[0.937s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.sh'
[0.938s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.ps1'
[0.941s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.dsv'
[0.944s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.sh'
[0.946s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.bash'
[0.948s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.zsh'
[0.950s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/colcon-core/packages/slpmu_jack)
[0.951s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path')
[0.952s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.ps1'
[0.953s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor
[0.953s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.dsv'
[0.954s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.sh'
[0.955s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path_multiarch')
[0.955s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.ps1'
[0.956s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.dsv'
[0.957s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.sh'
[0.957s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_motor)
[0.958s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor' for CMake module files
[0.959s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor' for CMake config files
[0.959s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'cmake_prefix_path')
[0.959s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.ps1'
[0.960s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.dsv'
[0.961s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.sh'
[0.962s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib'
[0.962s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin'
[0.962s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'path')
[0.962s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.ps1'
[0.963s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.dsv'
[0.964s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.sh'
[0.965s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig/slpmu_motor.pc'
[0.965s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/python3.12/site-packages'
[0.965s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin'
[0.966s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pythonscriptspath')
[0.966s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.ps1'
[0.967s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.dsv'
[0.967s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.sh'
[0.968s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.ps1'
[0.970s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.dsv'
[0.970s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.sh'
[0.971s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.bash'
[0.972s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.zsh'
[0.973s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/colcon-core/packages/slpmu_motor)
[0.973s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2' with build type 'ament_cmake'
[0.974s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2'
[0.974s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.975s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.007s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[1.419s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[1.424s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
