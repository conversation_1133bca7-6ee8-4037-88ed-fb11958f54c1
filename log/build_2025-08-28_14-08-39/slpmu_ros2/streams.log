[0.017s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[0.053s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.086s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.203s] -- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
[0.238s] -- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
[0.251s] -- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
[0.267s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.285s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.370s] -- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
[0.371s] -- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
[0.458s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.547s] -- Found std_msgs: 5.3.6 (/opt/ros/jazzy/share/std_msgs/cmake)
[0.565s] -- Found geometry_msgs: 5.3.6 (/opt/ros/jazzy/share/geometry_msgs/cmake)
[0.588s] -- Found nav_msgs: 5.3.6 (/opt/ros/jazzy/share/nav_msgs/cmake)
[0.624s] -- Found tf2: 0.36.12 (/opt/ros/jazzy/share/tf2/cmake)
[0.629s] -- Found tf2_ros: 0.36.12 (/opt/ros/jazzy/share/tf2_ros/cmake)
[0.779s] -- Found tf2_geometry_msgs: 0.36.12 (/opt/ros/jazzy/share/tf2_geometry_msgs/cmake)
[0.784s] -- Found eigen3_cmake_module: 0.3.0 (/opt/ros/jazzy/share/eigen3_cmake_module/cmake)
[0.786s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.788s] -- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
[0.821s] -- Found rosidl_default_generators: 1.6.0 (/opt/ros/jazzy/share/rosidl_default_generators/cmake)
[0.834s] -- Found rosidl_adapter: 4.6.5 (/opt/ros/jazzy/share/rosidl_adapter/cmake)
[0.838s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[1.208s] -- Found ament_cmake_ros: 0.12.0 (/opt/ros/jazzy/share/ament_cmake_ros/cmake)
[1.503s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.615s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.617s] -- Found python_cmake_module: 0.11.1 (/opt/ros/jazzy/share/python_cmake_module/cmake)
[1.948s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
[1.991s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.991s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include>
[1.991s] -- Configured cppcheck exclude dirs and/or files: 
[1.992s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.992s] -- Configured 'flake8' exclude dirs and/or files: 
[1.993s] -- Added test 'lint_cmake' to check CMake code style
[1.994s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.996s] -- Added test 'uncrustify' to check C / C++ code style
[1.996s] -- Configured uncrustify additional arguments: 
[1.996s] -- Added test 'xmllint' to check XML markup files
[2.002s] -- Configuring done (2.0s)
[2.079s] -- Generating done (0.1s)
[2.100s] -- Build files have been written to: /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[2.153s] [  1%] [34m[1mGenerating type hashes for ROS interfaces[0m
[2.166s] [  1%] Built target ament_cmake_python_copy_slpmu_ros2
[2.508s] running egg_info
[2.567s] writing slpmu_ros2.egg-info/PKG-INFO
[2.568s] writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt
[2.568s] writing top-level names to slpmu_ros2.egg-info/top_level.txt
[2.668s] reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[2.668s] writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[2.708s] [  1%] Built target ament_cmake_python_build_slpmu_ros2_egg
[2.767s] [  1%] Built target slpmu_ros2__rosidl_generator_type_description
[2.792s] [  3%] [34m[1mGenerating C code for ROS interfaces[0m
[2.798s] [  5%] [34m[1mGenerating C++ code for ROS interfaces[0m
[3.837s] [  5%] Built target slpmu_ros2__cpp
[3.875s] [  6%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_c.dir/rosidl_generator_c/slpmu_ros2/action/detail/jack_control__description.c.o[0m
[3.877s] [  8%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_c.dir/rosidl_generator_c/slpmu_ros2/action/detail/jack_control__type_support.c.o[0m
[3.885s] [ 10%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_c.dir/rosidl_generator_c/slpmu_ros2/action/detail/jack_control__functions.c.o[0m
[3.887s] [ 11%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_c.dir/rosidl_generator_c/slpmu_ros2/msg/detail/jack_status__description.c.o[0m
[3.950s] [ 13%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_c.dir/rosidl_generator_c/slpmu_ros2/msg/detail/jack_status__functions.c.o[0m
[3.968s] [ 15%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_c.dir/rosidl_generator_c/slpmu_ros2/msg/detail/jack_status__type_support.c.o[0m
[4.011s] [ 16%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_c.dir/rosidl_generator_c/slpmu_ros2/msg/detail/motor_state__description.c.o[0m
[4.071s] [ 18%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_c.dir/rosidl_generator_c/slpmu_ros2/msg/detail/motor_state__functions.c.o[0m
[4.075s] [ 20%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_c.dir/rosidl_generator_c/slpmu_ros2/msg/detail/motor_state__type_support.c.o[0m
[4.716s] [ 22%] [32m[1mLinking C shared library libslpmu_ros2__rosidl_generator_c.so[0m
[4.770s] [ 22%] Built target slpmu_ros2__rosidl_generator_c
[4.793s] [ 23%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[4.793s] [ 25%] [34m[1mGenerating C introspection for ROS interfaces[0m
[4.797s] [ 27%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[4.822s] [ 28%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[5.622s] [ 30%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/slpmu_ros2/action/jack_control__type_support.cpp.o[0m
[6.024s] [ 32%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/slpmu_ros2/action/detail/jack_control__type_support.c.o[0m
[6.260s] [ 33%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/slpmu_ros2/msg/detail/jack_status__type_support.c.o[0m
[6.272s] [ 35%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/slpmu_ros2/action/detail/jack_control__type_support_c.cpp.o[0m
[6.335s] [ 37%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/slpmu_ros2/msg/detail/motor_state__type_support.c.o[0m
[6.385s] [ 38%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/slpmu_ros2/action/detail/dds_fastrtps/jack_control__type_support.cpp.o[0m
[6.435s] [ 40%] [32m[1mLinking C shared library libslpmu_ros2__rosidl_typesupport_introspection_c.so[0m
[6.523s] [ 40%] Built target slpmu_ros2__rosidl_typesupport_introspection_c
[6.537s] [ 42%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/slpmu_ros2/msg/detail/jack_status__type_support_c.cpp.o[0m
[7.035s] [ 44%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/slpmu_ros2/msg/jack_status__type_support.cpp.o[0m
[7.247s] [ 45%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/slpmu_ros2/msg/detail/dds_fastrtps/jack_status__type_support.cpp.o[0m
[7.583s] [ 47%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/slpmu_ros2/msg/motor_state__type_support.cpp.o[0m
[8.094s] [ 49%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[8.119s] [ 50%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_cpp.so[0m
[8.336s] [ 50%] Built target slpmu_ros2__rosidl_typesupport_cpp
[8.365s] [ 52%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/slpmu_ros2/msg/detail/motor_state__type_support_c.cpp.o[0m
[8.454s] [ 54%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/slpmu_ros2/msg/detail/dds_fastrtps/motor_state__type_support.cpp.o[0m
[9.261s] [ 55%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[9.345s] [ 57%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_fastrtps_cpp.so[0m
[9.414s] [ 59%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_fastrtps_c.so[0m
[9.536s] [ 59%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp
[9.552s] [ 59%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c
[9.606s] [ 61%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_c.dir/rosidl_typesupport_c/slpmu_ros2/action/jack_control__type_support.cpp.o[0m
[9.614s] [ 62%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_c.dir/rosidl_typesupport_c/slpmu_ros2/msg/jack_status__type_support.cpp.o[0m
[9.628s] [ 64%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[9.725s] [ 66%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_c.dir/rosidl_typesupport_c/slpmu_ros2/msg/motor_state__type_support.cpp.o[0m
[9.746s] [ 67%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
[9.856s] [ 69%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_c.so[0m
[9.978s] [ 69%] Built target slpmu_ros2__rosidl_typesupport_c
[10.002s] [ 71%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o[0m
[10.545s] [ 72%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/slpmu_ros2/action/detail/jack_control__type_support.cpp.o[0m
[11.026s] [ 74%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/slpmu_ros2/msg/detail/jack_status__type_support.cpp.o[0m
[11.788s] [ 76%] [32mBuilding CXX object CMakeFiles/slpmu_ros2__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/slpmu_ros2/msg/detail/motor_state__type_support.cpp.o[0m
[12.581s] [ 77%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/jack_operator.cpp.o[0m
[12.671s] [ 79%] [32m[1mLinking CXX shared library libslpmu_ros2__rosidl_typesupport_introspection_cpp.so[0m
[12.814s] [ 79%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp
[12.864s] [ 79%] Built target slpmu_ros2
[12.939s] [ 81%] [34m[1mGenerating Python code for ROS interfaces[0m
[14.989s] [ 81%] Built target slpmu_ros2__py
[15.049s] [ 84%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_py.dir/rosidl_generator_py/slpmu_ros2/msg/_jack_status_s.c.o[0m
[15.049s] [ 84%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_py.dir/rosidl_generator_py/slpmu_ros2/action/_jack_control_s.c.o[0m
[15.258s] [ 86%] [32mBuilding C object CMakeFiles/slpmu_ros2__rosidl_generator_py.dir/rosidl_generator_py/slpmu_ros2/msg/_motor_state_s.c.o[0m
[15.664s] [ 88%] [32m[1mLinking C shared library libslpmu_ros2__rosidl_generator_py.so[0m
[15.727s] [ 88%] Built target slpmu_ros2__rosidl_generator_py
[15.770s] [ 89%] [32mBuilding C object CMakeFiles/slpmu_ros2_s__rosidl_typesupport_introspection_c.dir/rosidl_generator_py/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_introspection_c.c.o[0m
[15.771s] [ 91%] [32mBuilding C object CMakeFiles/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.dir/rosidl_generator_py/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_fastrtps_c.c.o[0m
[15.806s] [ 93%] [32mBuilding C object CMakeFiles/slpmu_ros2_s__rosidl_typesupport_c.dir/rosidl_generator_py/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_c.c.o[0m
[16.150s] [ 94%] [32m[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so[0m
[16.180s] [ 96%] [32m[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so[0m
[16.198s] [ 96%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c
[16.201s] [ 98%] [32m[1mLinking C shared module rosidl_generator_py/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so[0m
[16.232s] [ 98%] Built target slpmu_ros2_s__rosidl_typesupport_c
[16.249s] [ 98%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c
[37.256s] [100%] [32m[1mLinking CXX executable slpmu_node[0m
[37.537s] [100%] Built target slpmu_node
[37.552s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[37.554s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[37.566s] -- Install configuration: "Release"
[37.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/rosidl_interfaces/slpmu_ros2
[37.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/action/JackControl.json
[37.566s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/JackStatus.json
[37.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/MotorState.json
[37.567s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2
[37.567s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action
[37.567s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail
[37.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__struct.h
[37.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__functions.h
[37.567s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.h
[37.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__functions.c
[37.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.c
[37.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__description.c
[37.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/jack_control.h
[37.568s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg
[37.568s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/motor_state.h
[37.569s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_generator_c__visibility_control.h
[37.569s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail
[37.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.c
[37.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__functions.h
[37.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.c
[37.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__functions.c
[37.569s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__functions.h
[37.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__description.c
[37.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.h
[37.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__functions.c
[37.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__description.c
[37.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__struct.h
[37.570s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.h
[37.571s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__struct.h
[37.571s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/jack_status.h
[37.571s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/library_path.sh
[37.571s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/library_path.dsv
[37.571s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_generator_c.so
[37.572s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_generator_c.so" to ""
[37.572s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2
[37.572s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action
[37.572s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail
[37.572s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__rosidl_typesupport_fastrtps_c.h
[37.572s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg
[37.573s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail
[37.573s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__rosidl_typesupport_fastrtps_c.h
[37.573s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__rosidl_typesupport_fastrtps_c.h
[37.573s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[37.573s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_fastrtps_c.so
[37.574s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_fastrtps_c.so" to ""
[37.574s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2
[37.574s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action
[37.574s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail
[37.574s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__builder.hpp
[37.574s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__traits.hpp
[37.575s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.hpp
[37.575s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__struct.hpp
[37.575s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/jack_control.hpp
[37.575s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg
[37.575s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/motor_state.hpp
[37.575s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/jack_status.hpp
[37.575s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail
[37.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__traits.hpp
[37.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.hpp
[37.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__traits.hpp
[37.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__builder.hpp
[37.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__struct.hpp
[37.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.hpp
[37.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__builder.hpp
[37.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__struct.hpp
[37.576s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_generator_cpp__visibility_control.hpp
[37.577s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2
[37.577s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action
[37.577s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail
[37.577s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/dds_fastrtps
[37.577s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__rosidl_typesupport_fastrtps_cpp.hpp
[37.577s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg
[37.577s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail
[37.578s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/dds_fastrtps
[37.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__rosidl_typesupport_fastrtps_cpp.hpp
[37.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__rosidl_typesupport_fastrtps_cpp.hpp
[37.578s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[37.578s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_fastrtps_cpp.so
[37.579s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_fastrtps_cpp.so" to ""
[37.579s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2
[37.579s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action
[37.579s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail
[37.579s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__rosidl_typesupport_introspection_c.h
[37.579s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.c
[37.579s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg
[37.580s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/rosidl_typesupport_introspection_c__visibility_control.h
[37.580s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail
[37.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.c
[37.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.c
[37.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__rosidl_typesupport_introspection_c.h
[37.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__rosidl_typesupport_introspection_c.h
[37.580s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_introspection_c.so
[37.581s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_introspection_c.so" to ""
[37.581s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_c.so
[37.581s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_c.so" to ""
[37.582s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2
[37.582s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action
[37.582s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail
[37.582s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__rosidl_typesupport_introspection_cpp.hpp
[37.582s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/action/detail/jack_control__type_support.cpp
[37.582s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg
[37.582s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail
[37.583s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__type_support.cpp
[37.583s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/jack_status__rosidl_typesupport_introspection_cpp.hpp
[37.583s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__rosidl_typesupport_introspection_cpp.hpp
[37.583s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/include/slpmu_ros2/slpmu_ros2/msg/detail/motor_state__type_support.cpp
[37.583s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_introspection_cpp.so
[37.584s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_introspection_cpp.so" to ""
[37.584s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_cpp.so
[37.585s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_typesupport_cpp.so" to ""
[37.585s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/pythonpath.sh
[37.585s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/pythonpath.dsv
[37.585s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info
[37.585s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info/SOURCES.txt
[37.585s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info/PKG-INFO
[37.585s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info/top_level.txt
[37.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2-1.0.0-py3.12.egg-info/dependency_links.txt
[37.586s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2
[37.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_c.c
[37.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_fastrtps_c.c
[37.586s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action
[37.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action/__init__.py
[37.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action/_jack_control.py
[37.586s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action/_jack_control_s.c
[37.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/__init__.py
[37.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so
[37.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/_slpmu_ros2_s.ep.rosidl_typesupport_introspection_c.c
[37.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so
[37.587s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg
[37.587s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_jack_status.py
[37.588s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/__init__.py
[37.588s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_jack_status_s.c
[37.588s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_motor_state.py
[37.588s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_motor_state_s.c
[37.588s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so
[37.655s] Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2'...
[37.655s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/__init__.py'...
[37.655s] Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action'...
[37.655s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action/__init__.py'...
[37.655s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/action/_jack_control.py'...
[37.655s] Listing '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg'...
[37.655s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/__init__.py'...
[37.655s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_jack_status.py'...
[37.656s] Compiling '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/msg/_motor_state.py'...
[37.662s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so
[37.663s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_fastrtps_c.so" to ""
[37.663s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so
[37.664s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_introspection_c.so" to ""
[37.664s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so
[37.664s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages/slpmu_ros2/slpmu_ros2_s__rosidl_typesupport_c.so" to ""
[37.665s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_generator_py.so
[37.666s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/libslpmu_ros2__rosidl_generator_py.so" to ""
[37.666s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/action/JackControl.idl
[37.666s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/JackStatus.idl
[37.666s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/MotorState.idl
[37.666s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/action/JackControl.action
[37.666s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/JackStatus.msg
[37.666s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/msg/MotorState.msg
[37.667s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/slpmu_ros2/slpmu_node
[37.670s] -- Set non-toolchain portion of runtime path of "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/slpmu_ros2/slpmu_node" to ""
[37.671s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/launch
[37.671s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/launch/slpmu_ros2.launch.py
[37.671s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/config
[37.671s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/config/slpmu_ros2.yaml
[37.671s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/package_run_dependencies/slpmu_ros2
[37.671s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/parent_prefix_path/slpmu_ros2
[37.672s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/ament_prefix_path.sh
[37.672s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/ament_prefix_path.dsv
[37.672s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/path.sh
[37.672s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/environment/path.dsv
[37.672s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.bash
[37.672s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.sh
[37.672s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.zsh
[37.672s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/local_setup.dsv
[37.672s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv
[37.672s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/ament_index/resource_index/packages/slpmu_ros2
[37.673s] -- Old export file "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_cExport.cmake" will be replaced.  Removing files [/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_cExport-release.cmake].
[37.674s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_cExport.cmake
[37.674s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_cExport-release.cmake
[37.674s] -- Old export file "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cExport.cmake" will be replaced.  Removing files [/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cExport-release.cmake].
[37.674s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cExport.cmake
[37.674s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cExport-release.cmake
[37.675s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_cppExport.cmake
[37.675s] -- Old export file "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cppExport.cmake" will be replaced.  Removing files [/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cppExport-release.cmake].
[37.675s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cppExport.cmake
[37.675s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_typesupport_fastrtps_cppExport-release.cmake
[37.676s] -- Old export file "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cExport.cmake" will be replaced.  Removing files [/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cExport-release.cmake].
[37.676s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cExport.cmake
[37.677s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cExport-release.cmake
[37.677s] -- Old export file "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cExport.cmake" will be replaced.  Removing files [/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cExport-release.cmake].
[37.677s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cExport.cmake
[37.678s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cExport-release.cmake
[37.678s] -- Old export file "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cppExport.cmake" will be replaced.  Removing files [/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cppExport-release.cmake].
[37.678s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cppExport.cmake
[37.678s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_introspection_cppExport-release.cmake
[37.678s] -- Old export file "/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cppExport.cmake" will be replaced.  Removing files [/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cppExport-release.cmake].
[37.678s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cppExport.cmake
[37.678s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2__rosidl_typesupport_cppExport-release.cmake
[37.678s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_pyExport.cmake
[37.678s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/export_slpmu_ros2__rosidl_generator_pyExport-release.cmake
[37.679s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/rosidl_cmake-extras.cmake
[37.679s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/ament_cmake_export_dependencies-extras.cmake
[37.679s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/ament_cmake_export_include_directories-extras.cmake
[37.679s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/ament_cmake_export_libraries-extras.cmake
[37.679s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/ament_cmake_export_targets-extras.cmake
[37.679s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[37.679s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[37.679s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2Config.cmake
[37.679s] -- Up-to-date: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/cmake/slpmu_ros2Config-version.cmake
[37.680s] -- Installing: /home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.xml
[37.682s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
