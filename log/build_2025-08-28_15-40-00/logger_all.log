[0.145s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.145s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x775041ca9ca0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x775041ca9a00>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x775041ca9a00>>, mixin_verb=('build',))
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.190s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/works/source/amr_vcu_test_ws'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ros'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['cmake', 'python']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'cmake'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['python_setup_py']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python_setup_py'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore_ament_install'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_pkg']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_pkg'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_meta']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_meta'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ros']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ros'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['cmake', 'python']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'cmake'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['python_setup_py']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python_setup_py'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_pkg']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_pkg'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_meta']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_meta'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ros']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ros'
[0.229s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slcan' with type 'ros.cmake' and name 'slcan'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore_ament_install'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_pkg']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_meta']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_meta'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ros']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ros'
[0.230s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_jack' with type 'ros.cmake' and name 'slpmu_jack'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ros'
[0.232s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_motor' with type 'ros.cmake' and name 'slpmu_motor'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_meta']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_meta'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ros'
[0.233s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_power' with type 'ros.cmake' and name 'slpmu_power'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ros'
[0.234s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_process' with type 'ros.cmake' and name 'slpmu_process'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore_ament_install'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_pkg']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_pkg'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_meta']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_meta'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ros']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ros'
[0.236s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_ros2' with type 'ros.ament_cmake' and name 'slpmu_ros2'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ros'
[0.238s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sl_vcu_all' with type 'ros.ament_cmake' and name 'sl_vcu_all'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['ignore', 'ignore_ament_install']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ignore'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ignore_ament_install'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['colcon_pkg']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'colcon_pkg'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['colcon_meta']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'colcon_meta'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['ros']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ros'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['cmake', 'python']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'cmake'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'python'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['python_setup_py']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'python_setup_py'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['ignore', 'ignore_ament_install']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ignore'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ignore_ament_install'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['colcon_pkg']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'colcon_pkg'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['colcon_meta']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'colcon_meta'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['ros']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ros'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['cmake', 'python']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'cmake'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'python'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['python_setup_py']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'python_setup_py'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['ignore', 'ignore_ament_install']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ignore'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ignore_ament_install'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['colcon_pkg']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'colcon_pkg'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['colcon_meta']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'colcon_meta'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['ros']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ros'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['cmake', 'python']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'cmake'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'python'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['python_setup_py']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'python_setup_py'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['ignore', 'ignore_ament_install']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ignore'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ignore_ament_install'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['colcon_pkg']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'colcon_pkg'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['colcon_meta']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'colcon_meta'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['ros']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ros'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['cmake', 'python']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'cmake'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'python'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['python_setup_py']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'python_setup_py'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['ignore', 'ignore_ament_install']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ignore'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ignore_ament_install'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['colcon_pkg']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'colcon_pkg'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['colcon_meta']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'colcon_meta'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['ros']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ros'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['cmake', 'python']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'cmake'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'python'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['python_setup_py']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'python_setup_py'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['ignore', 'ignore_ament_install']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ignore'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ignore_ament_install'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['colcon_pkg']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'colcon_pkg'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['colcon_meta']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'colcon_meta'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['ros']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ros'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['cmake', 'python']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'cmake'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'python'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['python_setup_py']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'python_setup_py'
[0.244s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.244s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.245s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.245s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.245s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.266s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.266s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.270s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 393 installed packages in /opt/ros/jazzy
[0.271s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.336s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_args' from command line to 'None'
[0.336s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target' from command line to 'None'
[0.336s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.336s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_cache' from command line to 'False'
[0.336s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_first' from command line to 'False'
[0.336s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_force_configure' from command line to 'False'
[0.336s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'ament_cmake_args' from command line to 'None'
[0.336s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_cmake_args' from command line to 'None'
[0.336s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.336s] DEBUG:colcon.colcon_core.verb:Building package 'sl_vcu_all' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all', 'symlink_install': False, 'test_result_base': None}
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_args' from command line to 'None'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target' from command line to 'None'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_cache' from command line to 'False'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_first' from command line to 'False'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_force_configure' from command line to 'False'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'ament_cmake_args' from command line to 'None'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_cmake_args' from command line to 'None'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.337s] DEBUG:colcon.colcon_core.verb:Building package 'slcan' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan', 'symlink_install': False, 'test_result_base': None}
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_args' from command line to 'None'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target' from command line to 'None'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_cache' from command line to 'False'
[0.337s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_first' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_force_configure' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'ament_cmake_args' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_cmake_args' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.338s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_process' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process', 'symlink_install': False, 'test_result_base': None}
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_args' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_cache' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_first' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_force_configure' from command line to 'False'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'ament_cmake_args' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_cmake_args' from command line to 'None'
[0.338s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.338s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_jack' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack', 'symlink_install': False, 'test_result_base': None}
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_cache' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_first' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_force_configure' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'ament_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.339s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_motor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor', 'symlink_install': False, 'test_result_base': None}
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_cache' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_first' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_force_configure' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'ament_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.340s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_power' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power', 'symlink_install': False, 'test_result_base': None}
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_cache' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_first' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_force_configure' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'ament_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.340s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_ros2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2', 'symlink_install': False, 'test_result_base': None}
[0.340s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.342s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.342s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan' with build type 'cmake'
[0.342s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan'
[0.345s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.345s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.345s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.349s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process' with build type 'cmake'
[0.349s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process'
[0.349s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.349s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.353s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all' with build type 'ament_cmake'
[0.353s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all'
[0.354s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.354s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.362s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.367s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process -- -j4 -l4
[0.369s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[0.448s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.463s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.464s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process -- -j4 -l4
[0.465s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process
[0.476s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path')
[0.476s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.ps1'
[0.477s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.478s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.dsv'
[0.479s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.sh'
[0.482s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path_multiarch')
[0.482s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.ps1'
[0.483s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.dsv'
[0.483s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.sh'
[0.484s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slcan)
[0.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan' for CMake module files
[0.487s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan' for CMake config files
[0.487s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'cmake_prefix_path')
[0.487s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.ps1'
[0.488s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.dsv'
[0.488s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.sh'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/bin'
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig/slcan.pc'
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/python3.12/site-packages'
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/bin'
[0.490s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.ps1'
[0.491s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.dsv'
[0.492s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.sh'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.bash'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.zsh'
[0.494s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/colcon-core/packages/slcan)
[0.495s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor' with build type 'cmake'
[0.495s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor'
[0.495s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.495s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.499s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack' with build type 'cmake'
[0.500s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack'
[0.500s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.500s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.507s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process
[0.507s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path')
[0.507s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.ps1'
[0.508s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.dsv'
[0.509s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.sh'
[0.509s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path_multiarch')
[0.510s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.ps1'
[0.510s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.dsv'
[0.511s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.sh'
[0.512s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_process)
[0.512s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process' for CMake module files
[0.513s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process' for CMake config files
[0.513s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'cmake_prefix_path')
[0.513s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.ps1'
[0.514s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.dsv'
[0.514s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.sh'
[0.515s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib'
[0.515s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/bin'
[0.515s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig/slpmu_process.pc'
[0.515s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/python3.12/site-packages'
[0.516s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/bin'
[0.516s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.ps1'
[0.517s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.dsv'
[0.518s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.sh'
[0.518s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.bash'
[0.519s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.zsh'
[0.519s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/colcon-core/packages/slpmu_process)
[0.520s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power' with build type 'cmake'
[0.520s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power'
[0.520s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.521s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.527s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor -- -j4 -l4
[0.530s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[0.538s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power -- -j4 -l4
[0.706s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[0.708s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[0.724s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor -- -j4 -l4
[0.724s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor
[0.729s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path')
[0.729s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.ps1'
[0.730s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.dsv'
[0.730s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.sh'
[0.731s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path_multiarch')
[0.731s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.ps1'
[0.732s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.dsv'
[0.735s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[0.736s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.sh'
[0.736s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_jack)
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake module files
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake config files
[0.737s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'cmake_prefix_path')
[0.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.ps1'
[0.741s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.dsv'
[0.741s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.sh'
[0.742s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib'
[0.742s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[0.742s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'path')
[0.742s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.ps1'
[0.744s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.dsv'
[0.744s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.sh'
[0.747s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig/slpmu_jack.pc'
[0.747s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/python3.12/site-packages'
[0.748s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[0.748s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pythonscriptspath')
[0.748s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.ps1'
[0.751s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.dsv'
[0.753s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.sh'
[0.755s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.ps1'
[0.756s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.dsv'
[0.756s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.sh'
[0.757s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.bash'
[0.758s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.zsh'
[0.758s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/colcon-core/packages/slpmu_jack)
[0.760s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power -- -j4 -l4
[0.761s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power
[0.762s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path')
[0.763s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.ps1'
[0.763s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.dsv'
[0.763s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.sh'
[0.764s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path_multiarch')
[0.764s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.ps1'
[0.768s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.dsv'
[0.769s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.sh'
[0.769s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_motor)
[0.769s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor' for CMake module files
[0.770s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor' for CMake config files
[0.770s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'cmake_prefix_path')
[0.770s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.ps1'
[0.771s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor
[0.773s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.dsv'
[0.773s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.sh'
[0.774s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib'
[0.774s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin'
[0.774s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'path')
[0.774s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.ps1'
[0.775s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.dsv'
[0.775s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.sh'
[0.779s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig/slpmu_motor.pc'
[0.779s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/python3.12/site-packages'
[0.779s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin'
[0.780s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pythonscriptspath')
[0.781s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.ps1'
[0.781s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.dsv'
[0.782s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.sh'
[0.782s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.ps1'
[0.787s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.dsv'
[0.788s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.sh'
[0.789s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.bash'
[0.789s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.zsh'
[0.790s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/colcon-core/packages/slpmu_motor)
[0.791s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2' with build type 'ament_cmake'
[0.791s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2'
[0.791s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.791s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.797s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power
[0.797s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path')
[0.798s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.ps1'
[0.798s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.dsv'
[0.799s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.sh'
[0.800s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path_multiarch')
[0.801s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.ps1'
[0.801s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.dsv'
[0.802s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.sh'
[0.802s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_power)
[0.803s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power' for CMake module files
[0.804s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power' for CMake config files
[0.804s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'cmake_prefix_path')
[0.805s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.ps1'
[0.805s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.dsv'
[0.806s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.sh'
[0.807s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib'
[0.807s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin'
[0.808s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'path')
[0.808s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.ps1'
[0.809s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.dsv'
[0.809s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.sh'
[0.810s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/pkgconfig/slpmu_power.pc'
[0.811s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/python3.12/site-packages'
[0.811s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin'
[0.812s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pythonscriptspath')
[0.813s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.ps1'
[0.813s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.dsv'
[0.814s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.sh'
[0.814s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.ps1'
[0.815s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.dsv'
[0.816s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.sh'
[0.818s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.bash'
[0.818s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.zsh'
[0.820s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/colcon-core/packages/slpmu_power)
[0.824s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[1.133s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[1.134s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[1.454s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sl_vcu_all)
[1.454s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake module files
[1.455s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake config files
[1.456s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'cmake_prefix_path')
[1.457s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.ps1'
[1.462s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.dsv'
[1.462s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.sh'
[1.463s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[1.463s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib'
[1.464s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'ld_library_path_lib')
[1.464s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.ps1'
[1.465s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.dsv'
[1.466s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.sh'
[1.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/pkgconfig/sl_vcu_all.pc'
[1.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages'
[1.467s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'pythonpath')
[1.467s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.ps1'
[1.470s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.dsv'
[1.470s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.sh'
[1.474s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.474s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.ps1'
[1.475s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv'
[1.476s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.sh'
[1.476s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.bash'
[1.477s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.zsh'
[1.480s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/colcon-core/packages/sl_vcu_all)
[1.481s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sl_vcu_all)
[1.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake module files
[1.482s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake config files
[1.486s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'cmake_prefix_path')
[1.486s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.ps1'
[1.487s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.dsv'
[1.487s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.sh'
[1.488s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib'
[1.488s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'ld_library_path_lib')
[1.488s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.ps1'
[1.491s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.dsv'
[1.493s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.sh'
[1.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/pkgconfig/sl_vcu_all.pc'
[1.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages'
[1.494s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'pythonpath')
[1.494s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.ps1'
[1.495s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.dsv'
[1.495s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.sh'
[1.496s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.ps1'
[1.497s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv'
[1.498s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.sh'
[1.498s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.bash'
[1.499s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.zsh'
[1.499s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/colcon-core/packages/sl_vcu_all)
[21.278s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[21.280s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[21.297s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_ros2)
[21.297s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake module files
[21.298s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[21.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake config files
[21.299s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'cmake_prefix_path')
[21.299s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.ps1'
[21.300s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.dsv'
[21.300s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.sh'
[21.301s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib'
[21.301s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'ld_library_path_lib')
[21.301s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.ps1'
[21.302s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.dsv'
[21.302s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.sh'
[21.302s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[21.302s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/pkgconfig/slpmu_ros2.pc'
[21.303s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages'
[21.303s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'pythonpath')
[21.303s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.ps1'
[21.304s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.dsv'
[21.304s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.sh'
[21.305s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[21.305s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.ps1'
[21.306s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv'
[21.306s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.sh'
[21.307s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.bash'
[21.307s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.zsh'
[21.307s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/colcon-core/packages/slpmu_ros2)
[21.308s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_ros2)
[21.308s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake module files
[21.309s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake config files
[21.310s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'cmake_prefix_path')
[21.310s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.ps1'
[21.310s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.dsv'
[21.311s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.sh'
[21.311s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib'
[21.312s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'ld_library_path_lib')
[21.312s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.ps1'
[21.313s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.dsv'
[21.313s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.sh'
[21.313s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[21.314s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/pkgconfig/slpmu_ros2.pc'
[21.314s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages'
[21.314s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'pythonpath')
[21.314s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.ps1'
[21.315s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.dsv'
[21.315s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.sh'
[21.316s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[21.316s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.ps1'
[21.317s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv'
[21.317s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.sh'
[21.318s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.bash'
[21.318s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.zsh'
[21.319s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/colcon-core/packages/slpmu_ros2)
[21.319s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[21.319s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[21.319s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[21.320s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[21.339s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[21.339s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[21.339s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[21.351s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[21.351s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.ps1'
[21.352s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_ps1.py'
[21.353s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.ps1'
[21.354s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.sh'
[21.355s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_sh.py'
[21.356s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.sh'
[21.357s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.bash'
[21.357s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.bash'
[21.358s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.zsh'
[21.359s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.zsh'
