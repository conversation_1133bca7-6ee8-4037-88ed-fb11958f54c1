[0.015s] Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[0.102s] [  2%] Built target slpmu_ros2__rosidl_generator_type_description
[0.128s] [  2%] Built target ament_cmake_python_copy_slpmu_ros2
[0.140s] [ 15%] Built target slpmu_ros2__rosidl_generator_c
[0.147s] [ 18%] Built target slpmu_ros2__cpp
[0.189s] [ 26%] Built target slpmu_ros2__rosidl_typesupport_c
[0.199s] [ 34%] Built target slpmu_ros2__rosidl_typesupport_introspection_c
[0.201s] [ 42%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c
[0.233s] [ 50%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp
[0.246s] [ 57%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp
[0.252s] [ 65%] Built target slpmu_ros2__rosidl_typesupport_cpp
[0.295s] [ 65%] Built target slpmu_ros2
[0.328s] [ 68%] Built target slpmu_ros2__py
[0.361s] [ 71%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[0.365s] [ 73%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
[0.497s] [ 78%] Built target slpmu_ros2__rosidl_generator_py
[0.559s] running egg_info
[0.594s] [ 84%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c
[0.612s] writing slpmu_ros2.egg-info/PKG-INFO
[0.616s] writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt
[0.616s] writing top-level names to slpmu_ros2.egg-info/top_level.txt
[0.621s] [ 86%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o[0m
[0.788s] reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[0.789s] writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[0.789s] [ 86%] Built target ament_cmake_python_build_slpmu_ros2_egg
[0.834s] [ 92%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c
[0.835s] [ 97%] Built target slpmu_ros2_s__rosidl_typesupport_c
[3.898s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:[m[K In member function ‘[01m[Kvoid slpmu::node::PMUNode::[01;32m[Kinit_operators[m[K()[m[K’:
[3.898s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:201:29:[m[K [01;31m[Kerror: [m[K‘[01m[Kon_alarm_status[m[K’ is not a member of ‘[01m[Kslpmu::node::PMUNode[m[K’
[3.898s]   201 |         std::bind(&PMUNode::[01;31m[Kon_alarm_status[m[K, this, std::placeholders::_1));
[3.898s]       |                             [01;31m[K^~~~~~~~~~~~~~~[m[K
[3.937s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:[m[K At global scope:
[3.937s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:551:6:[m[K [01;31m[Kerror: [m[Kno declaration matches ‘[01m[Kvoid slpmu::node::PMUNode::[01;32m[Kon_motor_alarm_status[m[K(const slpmu::operators::ControlOperator::motor_alarm_status_t&)[m[K’
[3.938s]   551 | void [01;31m[KPMUNode[m[K::on_motor_alarm_status(const operators::ControlOperator::motor_alarm_status_t& alarm_status) {
[3.938s]       |      [01;31m[K^~~~~~~[m[K
[3.938s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:551:6:[m[K [01;36m[Knote: [m[Kno functions named ‘[01m[Kvoid slpmu::node::PMUNode::[01;32m[Kon_motor_alarm_status[m[K(const slpmu::operators::ControlOperator::motor_alarm_status_t&)[m[K’
[3.938s] In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:9[m[K:
[3.938s] [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:23:7:[m[K [01;36m[Knote: [m[K‘[01m[Kclass slpmu::node::PMUNode[m[K’ defined here
[3.938s]    23 | class [01;36m[KPMUNode[m[K : public rclcpp::Node {
[3.938s]       |       [01;36m[K^~~~~~~[m[K
[7.883s] gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:76: CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o] Error 1
[7.884s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/slpmu_node.dir/all] Error 2
[7.884s] gmake: *** [Makefile:146: all] Error 2
[7.886s] Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '2': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
