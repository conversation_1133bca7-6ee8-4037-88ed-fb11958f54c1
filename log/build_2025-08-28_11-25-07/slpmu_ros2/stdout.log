[  2%] Built target slpmu_ros2__rosidl_generator_type_description
[  2%] Built target ament_cmake_python_copy_slpmu_ros2
[ 15%] Built target slpmu_ros2__rosidl_generator_c
[ 18%] Built target slpmu_ros2__cpp
[ 26%] Built target slpmu_ros2__rosidl_typesupport_c
[ 34%] Built target slpmu_ros2__rosidl_typesupport_introspection_c
[ 42%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c
[ 50%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp
[ 57%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp
[ 65%] Built target slpmu_ros2__rosidl_typesupport_cpp
[ 65%] Built target slpmu_ros2
[ 68%] Built target slpmu_ros2__py
[ 71%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[ 73%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
[ 78%] Built target slpmu_ros2__rosidl_generator_py
running egg_info
[ 84%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c
writing slpmu_ros2.egg-info/PKG-INFO
writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt
writing top-level names to slpmu_ros2.egg-info/top_level.txt
[ 86%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o[0m
reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[ 86%] Built target ament_cmake_python_build_slpmu_ros2_egg
[ 92%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c
[ 97%] Built target slpmu_ros2_s__rosidl_typesupport_c
