[  2%] Built target slpmu_ros2__rosidl_generator_type_description
[  2%] Built target ament_cmake_python_copy_slpmu_ros2
[ 15%] Built target slpmu_ros2__rosidl_generator_c
[ 18%] Built target slpmu_ros2__cpp
[ 26%] Built target slpmu_ros2__rosidl_typesupport_c
[ 34%] Built target slpmu_ros2__rosidl_typesupport_introspection_c
[ 42%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_c
[ 50%] Built target slpmu_ros2__rosidl_typesupport_introspection_cpp
[ 57%] Built target slpmu_ros2__rosidl_typesupport_fastrtps_cpp
[ 65%] Built target slpmu_ros2__rosidl_typesupport_cpp
[ 65%] Built target slpmu_ros2
[ 68%] Built target slpmu_ros2__py
[ 71%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o[0m
[ 73%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/slpmu_ros2_main.cpp.o[0m
[ 78%] Built target slpmu_ros2__rosidl_generator_py
running egg_info
[ 84%] Built target slpmu_ros2_s__rosidl_typesupport_fastrtps_c
writing slpmu_ros2.egg-info/PKG-INFO
writing dependency_links to slpmu_ros2.egg-info/dependency_links.txt
writing top-level names to slpmu_ros2.egg-info/top_level.txt
[ 86%] [32mBuilding CXX object CMakeFiles/slpmu_node.dir/src/operators/control_operator.cpp.o[0m
reading manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
writing manifest file 'slpmu_ros2.egg-info/SOURCES.txt'
[ 86%] Built target ament_cmake_python_build_slpmu_ros2_egg
[ 92%] Built target slpmu_ros2_s__rosidl_typesupport_introspection_c
[ 97%] Built target slpmu_ros2_s__rosidl_typesupport_c
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:[m[K In member function ‘[01m[Kvoid slpmu::node::PMUNode::[01;32m[Kinit_operators[m[K()[m[K’:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:201:29:[m[K [01;31m[Kerror: [m[K‘[01m[Kon_alarm_status[m[K’ is not a member of ‘[01m[Kslpmu::node::PMUNode[m[K’
  201 |         std::bind(&PMUNode::[01;31m[Kon_alarm_status[m[K, this, std::placeholders::_1));
      |                             [01;31m[K^~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:[m[K At global scope:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:551:6:[m[K [01;31m[Kerror: [m[Kno declaration matches ‘[01m[Kvoid slpmu::node::PMUNode::[01;32m[Kon_motor_alarm_status[m[K(const slpmu::operators::ControlOperator::motor_alarm_status_t&)[m[K’
  551 | void [01;31m[KPMUNode[m[K::on_motor_alarm_status(const operators::ControlOperator::motor_alarm_status_t& alarm_status) {
      |      [01;31m[K^~~~~~~[m[K
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:551:6:[m[K [01;36m[Knote: [m[Kno functions named ‘[01m[Kvoid slpmu::node::PMUNode::[01;32m[Kon_motor_alarm_status[m[K(const slpmu::operators::ControlOperator::motor_alarm_status_t&)[m[K’
In file included from [01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/src/slpmu_node.cpp:9[m[K:
[01m[K/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2/include/slpmu/slpmu_node.hpp:23:7:[m[K [01;36m[Knote: [m[K‘[01m[Kclass slpmu::node::PMUNode[m[K’ defined here
   23 | class [01;36m[KPMUNode[m[K : public rclcpp::Node {
      |       [01;36m[K^~~~~~~[m[K
gmake[2]: *** [CMakeFiles/slpmu_node.dir/build.make:76: CMakeFiles/slpmu_node.dir/src/slpmu_node.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/slpmu_node.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
