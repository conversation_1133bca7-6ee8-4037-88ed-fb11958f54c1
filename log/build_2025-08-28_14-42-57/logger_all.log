[0.131s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.131s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x724e740a9d00>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x724e740a99a0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x724e740a99a0>>, mixin_verb=('build',))
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.176s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/works/source/amr_vcu_test_ws'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_pkg']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_pkg'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_meta']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_meta'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ros'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['cmake', 'python']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'cmake'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['python_setup_py']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python_setup_py'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_meta'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ros']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ros'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['cmake', 'python']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'cmake'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['python_setup_py']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python_setup_py'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_meta']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ros'
[0.215s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slcan' with type 'ros.cmake' and name 'slcan'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ros'
[0.216s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_jack' with type 'ros.cmake' and name 'slpmu_jack'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ros'
[0.217s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_motor' with type 'ros.cmake' and name 'slpmu_motor'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ros'
[0.219s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_power' with type 'ros.cmake' and name 'slpmu_power'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore_ament_install'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_pkg']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_pkg'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_meta']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ros'
[0.220s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_process' with type 'ros.cmake' and name 'slpmu_process'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ros'
[0.222s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_ros2' with type 'ros.ament_cmake' and name 'slpmu_ros2'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/sl_vcu_all) by extension 'ros'
[0.224s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sl_vcu_all' with type 'ros.ament_cmake' and name 'sl_vcu_all'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'ros'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['cmake', 'python']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'cmake'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'python'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extensions ['python_setup_py']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu) by extension 'python_setup_py'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['ignore', 'ignore_ament_install']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ignore'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ignore_ament_install'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['colcon_pkg']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'colcon_pkg'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['colcon_meta']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'colcon_meta'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['ros']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'ros'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['cmake', 'python']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'cmake'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'python'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extensions ['python_setup_py']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver) by extension 'python_setup_py'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['colcon_pkg']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'colcon_pkg'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['colcon_meta']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'colcon_meta'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['ros']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'ros'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['cmake', 'python']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'cmake'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'python'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extensions ['python_setup_py']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include) by extension 'python_setup_py'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['ignore', 'ignore_ament_install']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ignore'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ignore_ament_install'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['colcon_pkg']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'ros'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['cmake', 'python']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'cmake'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'python'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extensions ['python_setup_py']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio) by extension 'python_setup_py'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['colcon_pkg']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'colcon_pkg'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['colcon_meta']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'ros'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['cmake', 'python']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'cmake'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'python'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extensions ['python_setup_py']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/include/slgpio/driver) by extension 'python_setup_py'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ignore'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ignore_ament_install'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['colcon_pkg']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'colcon_pkg'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['colcon_meta']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'colcon_meta'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['ros']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'ros'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['cmake', 'python']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'cmake'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'python'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extensions ['python_setup_py']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/vcu/slgpio_driver/src) by extension 'python_setup_py'
[0.230s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.230s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.230s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.230s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.230s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.254s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.254s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.258s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 393 installed packages in /opt/ros/jazzy
[0.259s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.327s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_args' from command line to 'None'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target' from command line to 'None'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_cache' from command line to 'False'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_clean_first' from command line to 'False'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'cmake_force_configure' from command line to 'False'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'ament_cmake_args' from command line to 'None'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_cmake_args' from command line to 'None'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'sl_vcu_all' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.327s] DEBUG:colcon.colcon_core.verb:Building package 'sl_vcu_all' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all', 'symlink_install': False, 'test_result_base': None}
[0.327s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_args' from command line to 'None'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target' from command line to 'None'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_cache' from command line to 'False'
[0.327s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_first' from command line to 'False'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_force_configure' from command line to 'False'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'ament_cmake_args' from command line to 'None'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_cmake_args' from command line to 'None'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.328s] DEBUG:colcon.colcon_core.verb:Building package 'slcan' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan', 'symlink_install': False, 'test_result_base': None}
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_args' from command line to 'None'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target' from command line to 'None'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_cache' from command line to 'False'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_first' from command line to 'False'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_force_configure' from command line to 'False'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'ament_cmake_args' from command line to 'None'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_cmake_args' from command line to 'None'
[0.328s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.328s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_process' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process', 'symlink_install': False, 'test_result_base': None}
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_args' from command line to 'None'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target' from command line to 'None'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_cache' from command line to 'False'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_first' from command line to 'False'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_force_configure' from command line to 'False'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'ament_cmake_args' from command line to 'None'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_cmake_args' from command line to 'None'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.329s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_jack' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack', 'symlink_install': False, 'test_result_base': None}
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_args' from command line to 'None'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target' from command line to 'None'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_cache' from command line to 'False'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_first' from command line to 'False'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_force_configure' from command line to 'False'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'ament_cmake_args' from command line to 'None'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_cmake_args' from command line to 'None'
[0.329s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.330s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_motor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor', 'symlink_install': False, 'test_result_base': None}
[0.330s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_cache' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_first' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_force_configure' from command line to 'False'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'ament_cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_cmake_args' from command line to 'None'
[0.330s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.330s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_power' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power', 'symlink_install': False, 'test_result_base': None}
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_cache' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_first' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_force_configure' from command line to 'False'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'ament_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_cmake_args' from command line to 'None'
[0.331s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.331s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_ros2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2', 'merge_install': False, 'path': '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2', 'symlink_install': False, 'test_result_base': None}
[0.331s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.332s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.332s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan' with build type 'cmake'
[0.332s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slcan'
[0.335s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.335s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.336s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.339s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all' with build type 'ament_cmake'
[0.340s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/sl_vcu_all'
[0.340s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.340s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.344s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process' with build type 'cmake'
[0.344s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_process'
[0.344s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.344s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.353s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.356s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[0.360s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process -- -j4 -l4
[0.461s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slcan -- -j4 -l4
[0.494s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.510s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path')
[0.510s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.ps1'
[0.510s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.dsv'
[0.511s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slcan
[0.511s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path.sh'
[0.514s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path_multiarch')
[0.514s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.ps1'
[0.514s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.dsv'
[0.515s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.sh'
[0.516s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slcan)
[0.523s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan' for CMake module files
[0.523s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan' for CMake config files
[0.528s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'cmake_prefix_path')
[0.528s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.ps1'
[0.529s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.dsv'
[0.529s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/hook/cmake_prefix_path.sh'
[0.537s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib'
[0.537s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/bin'
[0.537s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig/slcan.pc'
[0.537s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/python3.12/site-packages'
[0.537s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/bin'
[0.538s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.ps1'
[0.542s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.dsv'
[0.543s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.sh'
[0.543s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.bash'
[0.551s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/slcan/package.zsh'
[0.552s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/share/colcon-core/packages/slcan)
[0.552s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor' with build type 'cmake'
[0.558s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_motor'
[0.558s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.561s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.584s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack' with build type 'cmake'
[0.585s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_jack'
[0.585s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.585s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.619s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor -- -j4 -l4
[0.635s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[1.004s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack -- -j4 -l4
[1.004s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[1.088s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor -- -j4 -l4
[1.089s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor
[1.119s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path')
[1.119s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.ps1'
[1.120s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.dsv'
[1.121s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_motor
[1.126s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.sh'
[1.126s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path_multiarch')
[1.127s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.ps1'
[1.128s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.dsv'
[1.132s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.sh'
[1.135s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_motor)
[1.136s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor' for CMake module files
[1.136s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor' for CMake config files
[1.137s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'cmake_prefix_path')
[1.137s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.ps1'
[1.137s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.dsv'
[1.139s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.sh'
[1.140s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib'
[1.140s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin'
[1.141s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'path')
[1.142s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.ps1'
[1.143s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.dsv'
[1.143s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/path.sh'
[1.146s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig/slpmu_motor.pc'
[1.146s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/python3.12/site-packages'
[1.147s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin'
[1.147s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pythonscriptspath')
[1.147s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.ps1'
[1.148s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.dsv'
[1.149s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.sh'
[1.151s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.ps1'
[1.153s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.dsv'
[1.154s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.sh'
[1.157s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.bash'
[1.157s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/slpmu_motor/package.zsh'
[1.160s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/share/colcon-core/packages/slpmu_motor)
[1.160s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power' with build type 'cmake'
[1.160s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_power'
[1.161s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.161s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.171s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_jack
[1.172s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path')
[1.172s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.ps1'
[1.174s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.dsv'
[1.175s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.sh'
[1.176s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path_multiarch')
[1.176s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.ps1'
[1.177s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.dsv'
[1.178s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.sh'
[1.182s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_jack)
[1.182s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake module files
[1.183s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack' for CMake config files
[1.183s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'cmake_prefix_path')
[1.183s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.ps1'
[1.184s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.dsv'
[1.184s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.sh'
[1.188s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib'
[1.188s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[1.188s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'path')
[1.188s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.ps1'
[1.189s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.dsv'
[1.190s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/path.sh'
[1.190s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/pkgconfig/slpmu_jack.pc'
[1.190s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/lib/python3.12/site-packages'
[1.191s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/bin'
[1.191s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pythonscriptspath')
[1.191s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.ps1'
[1.193s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.dsv'
[1.194s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.sh'
[1.196s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.ps1'
[1.198s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.dsv'
[1.199s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.sh'
[1.200s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.bash'
[1.203s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/slpmu_jack/package.zsh'
[1.203s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_jack/share/colcon-core/packages/slpmu_jack)
[1.209s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power -- -j4 -l4
[1.491s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power -- -j4 -l4
[1.493s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power
[1.510s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path')
[1.510s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.ps1'
[1.511s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.dsv'
[1.512s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.sh'
[1.512s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path_multiarch')
[1.513s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.ps1'
[1.513s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.dsv'
[1.514s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.sh'
[1.515s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_power)
[1.515s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power' for CMake module files
[1.516s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power' for CMake config files
[1.516s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'cmake_prefix_path')
[1.517s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.ps1'
[1.517s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.dsv'
[1.518s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.sh'
[1.519s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib'
[1.519s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin'
[1.519s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'path')
[1.520s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.ps1'
[1.521s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.dsv'
[1.521s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/path.sh'
[1.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/pkgconfig/slpmu_power.pc'
[1.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/lib/python3.12/site-packages'
[1.523s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/bin'
[1.523s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pythonscriptspath')
[1.523s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_power
[1.524s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.ps1'
[1.524s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.dsv'
[1.525s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.sh'
[1.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.ps1'
[1.527s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.dsv'
[1.527s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.sh'
[1.528s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.bash'
[1.528s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/slpmu_power/package.zsh'
[1.529s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_power/share/colcon-core/packages/slpmu_power)
[1.586s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all -- -j4 -l4
[1.588s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[1.718s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sl_vcu_all)
[1.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake module files
[1.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake config files
[1.720s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'cmake_prefix_path')
[1.721s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.ps1'
[1.722s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/sl_vcu_all
[1.722s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.dsv'
[1.723s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.sh'
[1.723s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib'
[1.723s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'ld_library_path_lib')
[1.724s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.ps1'
[1.724s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.dsv'
[1.725s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.sh'
[1.725s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.726s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/pkgconfig/sl_vcu_all.pc'
[1.726s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages'
[1.726s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'pythonpath')
[1.727s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.ps1'
[1.728s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.dsv'
[1.728s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.sh'
[1.729s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.729s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.ps1'
[1.730s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv'
[1.730s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.sh'
[1.731s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.bash'
[1.731s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.zsh'
[1.732s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/colcon-core/packages/sl_vcu_all)
[1.733s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sl_vcu_all)
[1.733s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake module files
[1.734s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all' for CMake config files
[1.735s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'cmake_prefix_path')
[1.735s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.ps1'
[1.736s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.dsv'
[1.736s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/cmake_prefix_path.sh'
[1.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib'
[1.737s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'ld_library_path_lib')
[1.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.ps1'
[1.738s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.dsv'
[1.738s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/ld_library_path_lib.sh'
[1.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/pkgconfig/sl_vcu_all.pc'
[1.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages'
[1.739s] Level 1:colcon.colcon_core.shell:create_environment_hook('sl_vcu_all', 'pythonpath')
[1.739s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.ps1'
[1.740s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.dsv'
[1.740s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/hook/pythonpath.sh'
[1.741s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/bin'
[1.741s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.ps1'
[1.742s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.dsv'
[1.742s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.sh'
[1.743s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.bash'
[1.743s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/sl_vcu_all/package.zsh'
[1.744s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/share/colcon-core/packages/sl_vcu_all)
[1.813s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process -- -j4 -l4
[1.814s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process
[1.827s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path')
[1.827s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.ps1'
[1.828s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_process
[1.828s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.dsv'
[1.829s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.sh'
[1.829s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path_multiarch')
[1.829s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.ps1'
[1.830s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.dsv'
[1.830s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.sh'
[1.831s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_process)
[1.831s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process' for CMake module files
[1.831s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process' for CMake config files
[1.832s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'cmake_prefix_path')
[1.832s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.ps1'
[1.832s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.dsv'
[1.833s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.sh'
[1.833s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib'
[1.833s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/bin'
[1.834s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig/slpmu_process.pc'
[1.834s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/python3.12/site-packages'
[1.834s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/bin'
[1.834s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.ps1'
[1.835s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.dsv'
[1.836s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.sh'
[1.836s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.bash'
[1.837s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/slpmu_process/package.zsh'
[1.837s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/share/colcon-core/packages/slpmu_process)
[1.838s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2' with build type 'ament_cmake'
[1.838s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/works/source/amr_vcu_test_ws/src/pmu/slpmu_ros2'
[1.838s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.839s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.856s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[27.547s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2 -- -j4 -l4
[27.549s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[27.567s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_ros2)
[27.567s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake module files
[27.568s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2' returned '0': AMENT_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan:/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib:${LD_LIBRARY_PATH} PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/bin:${PATH} PKG_CONFIG_PATH=/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/works/source/amr_vcu_test_ws/install/slcan/lib/pkgconfig PS1=\u@\h \[\033[01;36m\]\w\[\033[01;32m\]$(git_branch)\[\033[00m\] \$ PYTHONPATH=/home/<USER>/works/source/amr_vcu_test_ws/install/sl_vcu_all/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/works/source/amr_vcu_test_ws/build/slpmu_ros2
[27.569s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake config files
[27.570s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'cmake_prefix_path')
[27.570s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.ps1'
[27.571s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.dsv'
[27.571s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.sh'
[27.572s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib'
[27.572s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'ld_library_path_lib')
[27.572s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.ps1'
[27.572s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.dsv'
[27.573s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.sh'
[27.573s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[27.573s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/pkgconfig/slpmu_ros2.pc'
[27.574s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages'
[27.574s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'pythonpath')
[27.574s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.ps1'
[27.575s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.dsv'
[27.575s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.sh'
[27.576s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[27.576s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.ps1'
[27.577s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv'
[27.577s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.sh'
[27.578s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.bash'
[27.578s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.zsh'
[27.579s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/colcon-core/packages/slpmu_ros2)
[27.579s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_ros2)
[27.580s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake module files
[27.580s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2' for CMake config files
[27.581s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'cmake_prefix_path')
[27.581s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.ps1'
[27.582s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.dsv'
[27.582s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.sh'
[27.583s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib'
[27.583s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'ld_library_path_lib')
[27.583s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.ps1'
[27.584s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.dsv'
[27.584s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.sh'
[27.585s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[27.585s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/pkgconfig/slpmu_ros2.pc'
[27.585s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/lib/python3.12/site-packages'
[27.585s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'pythonpath')
[27.585s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.ps1'
[27.586s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.dsv'
[27.586s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/hook/pythonpath.sh'
[27.587s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/bin'
[27.588s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.ps1'
[27.588s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv'
[27.589s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.sh'
[27.589s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.bash'
[27.590s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/slpmu_ros2/package.zsh'
[27.591s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/works/source/amr_vcu_test_ws/install/slpmu_ros2/share/colcon-core/packages/slpmu_ros2)
[27.591s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[27.591s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[27.592s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[27.592s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[27.610s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[27.610s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[27.610s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[27.622s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[27.623s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.ps1'
[27.624s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_ps1.py'
[27.625s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.ps1'
[27.626s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.sh'
[27.627s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/works/source/amr_vcu_test_ws/install/_local_setup_util_sh.py'
[27.628s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.sh'
[27.629s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.bash'
[27.629s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.bash'
[27.630s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/works/source/amr_vcu_test_ws/install/local_setup.zsh'
[27.631s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/works/source/amr_vcu_test_ws/install/setup.zsh'
